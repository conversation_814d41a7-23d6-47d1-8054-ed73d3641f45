param([int]$TimeframeHours = 3)

$SERVER = "servidor-tqw"
$PORT = "2277"
$DEST_BASE = "C:/wamp64/www/op_Tqw/APP_TQW/dist"

Write-Host "Iniciando transferencia de archivos modificados en las ultimas $TimeframeHours horas..." -ForegroundColor Green

$timeLimit = (Get-Date).AddHours(-$TimeframeHours)

$directoriesToCheck = @(
    @{ Path = "."; Filter = "*.php"; Dest = "$DEST_BASE/" },
    @{ Path = "."; Filter = "*.html"; Dest = "$DEST_BASE/" },
    @{ Path = "js"; Filter = "*.*"; Dest = "$DEST_BASE/js/" },
    @{ Path = "css"; Filter = "*.*"; Dest = "$DEST_BASE/css/" },
    @{ Path = "components"; Filter = "*.*"; Dest = "$DEST_BASE/components/" },
    @{ Path = "includes"; Filter = "*.*"; Dest = "$DEST_BASE/includes/" },
    @{ Path = "js/modules"; Filter = "*.*"; Dest = "$DEST_BASE/js/modules/" },
    @{ Path = "js/modules/calidadReactiva"; Filter = "*.*"; Dest = "$DEST_BASE/js/modules/calidadReactiva/" },
    @{ Path = "js/mod_logistica"; Filter = "*.*"; Dest = "$DEST_BASE/js/mod_logistica/" },
    @{ Path = "config"; Filter = "*.*"; Dest = "$DEST_BASE/config/" }
)

foreach ($dir in $directoriesToCheck) {
    if (Test-Path $dir.Path) {
        $recentFiles = Get-ChildItem -Path $dir.Path -Filter $dir.Filter -File | Where-Object { $_.LastWriteTime -gt $timeLimit }
        
        if ($recentFiles.Count -gt 0) {
            Write-Host "Transfiriendo archivos de $($dir.Path)..." -ForegroundColor Yellow
            
            # Crear directorio remoto si no existe
            ssh -p $PORT $SERVER "mkdir -p $($dir.Dest)"

            foreach ($file in $recentFiles) {
                Write-Host "  Transfiriendo $($file.Name)..." -ForegroundColor Cyan
                scp -P $PORT $file.FullName "${SERVER}:$($dir.Dest)"
                
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "  OK $($file.Name)" -ForegroundColor Green
                } else {
                    Write-Host "  ERROR $($file.Name)" -ForegroundColor Red
                }
            }
        }
    }
}

Write-Host "Transferencia completada!" -ForegroundColor Green