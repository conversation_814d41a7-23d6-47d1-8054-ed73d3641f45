/**
 * handlers.js
 * Manejadores de eventos para el módulo de logística
 * Extraído del bloque JavaScript monolítico en mod_logistica.php
 */

// Función principal para redirigir en transferencia/instalación
function redirigirEnTransferencia(serial, item, ID_MOVI, accion) {
    console.log('🔄 redirigirEnTransferencia LLAMADA:', { serial, item, ID_MOVI, accion });
    
    // Validaciones comunes para INSTALA y TRANSFIERE
    function validarEstado() {
        if (ID_MOVI === '5') {
            console.log('⚠️ Estado 5: Ya declarado como instalado');
            mostrarNotificacion('Serial ya fue declarado como instalado', 'warning');
            return false;
        } else if (ID_MOVI === '3') {
            console.log('⚠️ Estado 3: Pendiente por el usuario');
            mostrarNotificacion('Aun pendiente por el usuario a quien escalaste el material', 'warning');
            return false;
        } else if (ID_MOVI === '4') {
            console.log('⚠️ Estado 4: Justificado por supervisor');
            mostrarNotificacion('El material ya fue justificado por el supervisor', 'warning');
            return false;
        } else if (ID_MOVI === '13') {
            console.log('⚠️ Estado 13: Escalado a VTR');
            mostrarNotificacion('El material ha sido escalado a VTR', 'warning');
            return false;
        }
        return true;
    }

    // Función auxiliar para preparar y mostrar offcanvas
    function prepararOffcanvas(targetId, inputId, serial) {
        try {
            // Asegurarnos de que el body esté en un estado consistente
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
            
            // Eliminar cualquier backdrop existente antes de crear uno nuevo
            const backdropElements = document.querySelectorAll('.offcanvas-backdrop');
            backdropElements.forEach(element => {
                element.remove();
            });
            
            // Cerrar cualquier otro offcanvas abierto
            document.querySelectorAll('.offcanvas.show').forEach(offcanvasEl => {
                const instance = bootstrap.Offcanvas.getInstance(offcanvasEl);
                if (instance) {
                    try {
                        instance.hide();
                    } catch (e) {
                        console.warn('Error al ocultar offcanvas:', e);
                    }
                }
            });
            
            // Asignar valor al campo de serie
            const serieInput = document.getElementById(inputId);
            if (serieInput) {
                serieInput.value = serial;
                console.log('🔍 Valor de ' + inputId + ' establecido:', serial);
            } else {
                console.error('No se encontró el campo ' + inputId);
            }
            
            const offcanvasElement = document.getElementById(targetId);
            console.log('🔍 Elemento offcanvas encontrado:', !!offcanvasElement);
            
            if (!offcanvasElement) {
                throw new Error('No se encontró el elemento offcanvas ' + targetId);
            }
            
            // Asegurarse de que tiene el atributo data-bs-backdrop="true"
            offcanvasElement.setAttribute('data-bs-backdrop', 'true');
            
            // Asegurarse de que el offcanvas no esté en estado show
            offcanvasElement.classList.remove('show');
            
            // Esperar un breve momento para asegurar que cualquier instancia previa se haya limpiado
            setTimeout(() => {
                try {
                    // Crear el objeto offcanvas con opciones explícitas
                    const offcanvas = new bootstrap.Offcanvas(offcanvasElement, {
                        backdrop: true,
                        scroll: false
                    });
                    
                    // Mostrar el offcanvas
                    offcanvas.show();
                    console.log('🔓 Offcanvas ' + targetId + ' abierto');
                } catch (innerError) {
                    console.error('Error al mostrar offcanvas:', innerError);
                    mostrarNotificacion('Ocurrió un error al mostrar el formulario', 'danger');
                }
            }, 50);
        } catch (error) {
            console.error('Error general al procesar acción:', error);
            mostrarNotificacion('Error al mostrar el formulario', 'danger');
        }
    }
    
    if (accion === 'INSTALA') {
        console.log('🏠 Procesando instalación');
        // Validar estado del material
        if (!validarEstado()) return;
        
        prepararOffcanvas('offcanvasInstalar', 'serieInstalar', serial);
    } else if (accion === 'TRANSFIERE') {
        console.log('📤 Procesando transferencia');
        // Validar estado del material
        if (!validarEstado()) return;
        
        prepararOffcanvas('offcanvasTransferir', 'serieTransferir', serial);
    }
}

// Función para actualizar registro (aceptar/rechazar material)
function actualizarRegistro(Serial, ticket, id_tecnico, accion) {
    // Obtener el elemento del botón que disparó el evento
    const buttonElement = event.target.closest('button');
    
    // Crear FormData para enviar los datos en POST
    var formData = new FormData();
    
    if (accion === 'ENTREGA_REV') {
        if (id_tecnico === '6') {
            mostrarNotificacion('Bodega tiene pendiente la confirmación', 'warning');
        } else if (id_tecnico === '7') {
            mostrarNotificacion('Material pendiente de revisión por supervisor', 'warning');
        } else {
            // Lógica para entrega reversa
            formData.append('Serial', Serial);
            formData.append('accion', accion);
            formData.append('id_tecnico_origen', window.userId);
            formData.append('ticket', ticket);
            
            enviarSolicitudAceptacion(formData, buttonElement);
        }
    } else {
        if (accion === 'ACEPTA') {
            // ID_MOVIMIENTO - Validación específica
            if (id_tecnico == 2) {
                mostrarNotificacion("NO PODRÁS CONFIRMAR HASTA LA REVISIÓN DE BODEGA", 'warning');
            } else {
                // Preparar datos para aceptación
                formData.append('Serial', Serial);
                formData.append('accion', accion);
                formData.append('id_tecnico_origen', window.userId);
                formData.append('ticket', ticket);
                
                enviarSolicitudAceptacion(formData, buttonElement);
            }
        }
    }
}

// Función auxiliar para enviar la solicitud de aceptación
function enviarSolicitudAceptacion(formData, buttonElement) {
    // Obtener el serial del material antes de procesar
    const row = buttonElement.closest('tr');
    const serialCell = row ? row.querySelector('td:first-child') : null;
    const serialNumber = serialCell ? serialCell.textContent.trim() : null;
    
    // Realiza la solicitud POST utilizando AJAX
    var request = new XMLHttpRequest();
    request.open('POST', 'GET_LOGIS_DIRECTA.php');
    
    request.onload = function() {
        // Procesa la respuesta del servidor
        if (request.status === 200) {
            // Mostrar mensaje de éxito usando la función de notificación en lugar de alert
            mostrarNotificacion('Material aceptado correctamente', 'success');
            
            // ACTUALIZAR CACHE de manera inteligente en lugar de limpiarlo completamente
            updateCacheAfterAcceptance(serialNumber);
            console.log('🔄 Cache actualizado inteligentemente para serial:', serialNumber);
            
            // ELIMINAR LA FILA INMEDIATAMENTE de recepción con animación
            if (row && serialNumber) {
                console.log('🗑️ Eliminando fila de recepción inmediatamente:', serialNumber);
                
                // Usar la función centralizada para remover con animación
                removeRowWithAnimation(row, () => {
                    // Mostrar mensaje informativo
                    const recepcionTableBody = document.getElementById('recepcionTableBody');
                    if (recepcionTableBody && recepcionTableBody.children.length === 0) {
                        recepcionTableBody.innerHTML = `
                            <tr>
                                <td colspan="3" class="text-center text-muted">
                                    <i class="bi bi-inbox me-2"></i>
                                    No hay materiales pendientes de recepción
                                </td>
                            </tr>
                        `;
                    }
                });
            }
            
        } else {
            console.error('❌ Error en solicitud:', request.status, request.statusText);
            mostrarNotificacion('Error al procesar la solicitud. Código: ' + request.status, 'danger');
        }
    };
    
    request.onerror = function() {
        console.error('❌ Error de red en solicitud');
        mostrarNotificacion('Error de conexión. Verifique su red e intente nuevamente.', 'danger');
    };
    
    request.send(formData);
}

// Función para transferir registro
function transferirRegistro(Serial, ticket, accion, id_tecnico_destino, motivo) {
    // Mostrar indicador de carga en el botón
    const submitButton = document.querySelector('.submit-offcanvas-btn');
    if (submitButton) {
        addButtonSpinner(submitButton, 'Enviando...');
    }

    // Preparar datos para envío
    const postData = `Serial=${encodeURIComponent(Serial)}&ticket=${encodeURIComponent(ticket)}&accion=${encodeURIComponent(accion)}&id_tecnico_destino=${encodeURIComponent(id_tecnico_destino)}&id_tecnico_origen=${encodeURIComponent(window.userId)}&motivo=${encodeURIComponent(motivo)}`;

    return new Promise((resolve, reject) => {
        const request = new XMLHttpRequest();
        request.timeout = 30000;

        request.onreadystatechange = function() {
            if (request.readyState === XMLHttpRequest.DONE) {
                if (request.status === 200) {
                    try {
                        const response = JSON.parse(request.responseText);
                        if (response.success) {
                            mostrarNotificacion('Transferencia realizada exitosamente', 'success');
                            
                            // Invalidar cache para forzar recarga
                            invalidateTableCache('directa');
                            
                            // Cerrar offcanvas
                            const offcanvasElement = document.getElementById('offcanvasTransferir');
                            if (offcanvasElement) {
                                const offcanvasInstance = bootstrap.Offcanvas.getInstance(offcanvasElement);
                                if (offcanvasInstance) {
                                    offcanvasInstance.hide();
                                }
                            }
                            
                            resolve(response);
                        } else {
                            reject(new Error(response.message || 'Error en la transferencia'));
                        }
                    } catch (e) {
                        reject(new Error('Error parseando respuesta del servidor'));
                    }
                } else {
                    reject(new Error('Error de conexión con el servidor'));
                }
                
                // Remover spinner del botón
                if (submitButton) {
                    removeButtonSpinner(submitButton, 'Transferir');
                }
            }
        };

        request.ontimeout = function() {
            if (submitButton) {
                removeButtonSpinner(submitButton, 'Transferir');
            }
            reject(new Error('Tiempo de espera agotado'));
        };

        request.open('POST', 'GET_LOGIS_DIRECTA.php');
        request.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        request.send(postData);
    });
}

// Función para mostrar modal de rechazo
function rechazoMaterial(Serial, ticket, id_tecnico_destino, accion) {
    const modal = document.getElementById('popup');
    if (modal) {
        modal.style.display = 'flex';
        
        // Configurar campos ocultos si existen
        const serialField = document.getElementById('serial_rechazar');
        const ticketField = document.getElementById('ticket_rechazar');
        const tecnicoField = document.getElementById('id_tecnico_destino_rechazar');
        const accionField = document.getElementById('accion_rechazar');
        
        if (serialField) serialField.value = Serial;
        if (ticketField) ticketField.value = ticket;
        if (tecnicoField) tecnicoField.value = id_tecnico_destino;
        if (accionField) accionField.value = accion;
    }
}

// Función para aceptar rechazo
function Rechazoaceptar() {
    const motivoField = document.getElementById('motivoRechazo');
    const serialField = document.getElementById('serial_rechazar');
    const ticketField = document.getElementById('ticket_rechazar');
    const tecnicoField = document.getElementById('id_tecnico_destino_rechazar');
    const accionField = document.getElementById('accion_rechazar');
    
    if (!motivoField || !motivoField.value.trim()) {
        mostrarNotificacion('Debe ingresar un motivo para el rechazo', 'warning');
        return;
    }
    
    const formData = new FormData();
    formData.append('Serial', serialField.value);
    formData.append('ticket', ticketField.value);
    formData.append('id_tecnico_destino', tecnicoField.value);
    formData.append('accion', accionField.value);
    formData.append('motivo_rechazo', motivoField.value);
    formData.append('id_tecnico_origen', window.userId);
    
    // Enviar solicitud de rechazo
    rechazarMaterial(formData)
        .then(response => {
            mostrarNotificacion('Material rechazado correctamente', 'success');
            Rechazocancelar(); // Cerrar modal
        })
        .catch(error => {
            mostrarNotificacion('Error al rechazar material: ' + error.message, 'danger');
        });
}

// Función para cancelar rechazo
function Rechazocancelar() {
    const modal = document.getElementById('popup');
    if (modal) {
        modal.style.display = 'none';
        
        // Limpiar campos
        const motivoField = document.getElementById('motivoRechazo');
        if (motivoField) motivoField.value = '';
    }
}

// Manejadores de eventos de offcanvas
function handleOffcanvasTransferirTecnicoClick() {
    toggleElementTransferencia('tecnicoTransf', true);
}

function handleOffcanvasJustificarClick() {
    toggleElementTransferencia('motivoASuper', true);
}

function handleOffcanvasBodegaSistemicoClick() {
    toggleElementTransferencia('motivo_tran_contain', true);
}

function handleOffcanvasBodegaSeriIncorrectaClick() {
    toggleElementTransferencia('serie_tran_contain', true);
}

function handleOffcanvasBodegaLinkClick() {
    toggleElementTransferencia('divArchivo', true);
}

function handleOffcanvasBodegaTOAClick() {
    toggleElementTransferencia('divArchivo', true);
}

function handleOffcanvasFotoCierreInvClick() {
    toggleElementTransferencia('divArchivo', true);
}

function handleOffcanvasDevueltoBodegaClick() {
    toggleElementTransferencia('divArchivo', true);
}

// Función auxiliar para actualizar cache después de aceptación
function updateCacheAfterAcceptance(serialNumber) {
    const tableCache = window.ModLogisticaConfig.tableCache;
    
    if (tableCache.recepcion && Array.isArray(tableCache.recepcion)) {
        // Remover el elemento de recepción
        tableCache.recepcion = tableCache.recepcion.filter(item => {
            const serial = item.Serial || item.serie;
            return serial !== serialNumber;
        });
        console.log('🔄 Serial removido del cache de recepción:', serialNumber);
    }
    
    // Invalidar cache de directa para que se recargue con el nuevo material
    if (tableCache.directa) {
        tableCache.directa = null;
        window.ModLogisticaConfig.loadingState.directa = false;
        console.log('🔄 Cache de directa invalidado para recarga');
    }
}

// Función para declarar instalación
function declararInstalacion(serie, idMovimiento) {
    const offcanvasElement = document.getElementById('offcanvasInstalar');
    const serieInput = document.getElementById('serieInstalar');
    
    if (offcanvasElement && serieInput) {
        serieInput.value = serie;
        const instalarOffcanvas = new bootstrap.Offcanvas(offcanvasElement);
        instalarOffcanvas.show();
    }
}

// Función para configurar todos los event listeners
function setupEventListeners() {
    // Event listeners del documento
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🔧 Configurando event listeners del módulo de logística');
        
        // Configurar botones de offcanvas si existen
        setupOffcanvasButtons();
        
        // Configurar formularios
        setupFormHandlers();
        
        // Configurar sistema de limpieza de offcanvas
        setupOffcanvasCleanup();
    });
}

// Función para configurar botones de offcanvas
function setupOffcanvasButtons() {
    // Manejadores específicos para cada tipo de offcanvas
    const offcanvasHandlers = {
        'handleOffcanvasTransferirTecnicoClick': handleOffcanvasTransferirTecnicoClick,
        'handleOffcanvasJustificarClick': handleOffcanvasJustificarClick,
        'handleOffcanvasBodegaSistemicoClick': handleOffcanvasBodegaSistemicoClick,
        'handleOffcanvasBodegaSeriIncorrectaClick': handleOffcanvasBodegaSeriIncorrectaClick,
        'handleOffcanvasBodegaLinkClick': handleOffcanvasBodegaLinkClick,
        'handleOffcanvasBodegaTOAClick': handleOffcanvasBodegaTOAClick,
        'handleOffcanvasFotoCierreInvClick': handleOffcanvasFotoCierreInvClick,
        'handleOffcanvasDevueltoBodegaClick': handleOffcanvasDevueltoBodegaClick
    };
    
    // Configurar handlers para elementos que existan
    Object.keys(offcanvasHandlers).forEach(handlerName => {
        const elements = document.querySelectorAll(`[onclick*="${handlerName}"]`);
        elements.forEach(element => {
            element.addEventListener('click', offcanvasHandlers[handlerName]);
        });
    });
}

// Función para configurar manejadores de formularios
function setupFormHandlers() {
    // Configurar formulario de transferencia
    const transferForm = document.getElementById('transferForm');
    if (transferForm) {
        transferForm.addEventListener('submit', handleTransferSubmit);
    }
    
    // Configurar formulario de instalación
    const installForm = document.getElementById('installForm');
    if (installForm) {
        installForm.addEventListener('submit', handleInstallSubmit);
    }
}

// Función para configurar limpieza de offcanvas
function setupOffcanvasCleanup() {
    document.body.addEventListener('hidden.bs.offcanvas', function() {
        removeModalOpenClass();
    });
}

// Manejador de envío de formulario de transferencia
function handleTransferSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const serial = formData.get('serie');
    const motivo = formData.get('motivo');
    const tecnicoDestino = formData.get('tecnico_destino');
    
    if (!serial || !motivo || !tecnicoDestino) {
        mostrarNotificacion('Todos los campos son obligatorios', 'warning');
        return;
    }
    
    transferirRegistro(serial, '', 'TRANSFIERE', tecnicoDestino, motivo)
        .then(() => {
            e.target.reset();
        })
        .catch(error => {
            mostrarNotificacion('Error en transferencia: ' + error.message, 'danger');
        });
}

// Manejador de envío de formulario de instalación
function handleInstallSubmit(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    
    declararInstalacion(formData)
        .then(() => {
            mostrarNotificacion('Instalación declarada correctamente', 'success');
            e.target.reset();
        })
        .catch(error => {
            mostrarNotificacion('Error al declarar instalación: ' + error.message, 'danger');
        });
}

// Exportar funciones al scope global para compatibilidad
window.ModLogisticaHandlers = {
    redirigirEnTransferencia,
    actualizarRegistro,
    enviarSolicitudAceptacion,
    transferirRegistro,
    rechazoMaterial,
    Rechazoaceptar,
    Rechazocancelar,
    declararInstalacion,
    setupEventListeners,
    updateCacheAfterAcceptance
};

// Compatibilidad con funciones globales existentes
window.redirigirEnTransferencia = redirigirEnTransferencia;
window.actualizarRegistro = actualizarRegistro;
window.transferirRegistro = transferirRegistro;
window.rechazoMaterial = rechazoMaterial;
window.Rechazoaceptar = Rechazoaceptar;
window.Rechazocancelar = Rechazocancelar;
window.declararInstalacion = declararInstalacion;

// Inicializar event listeners
setupEventListeners();

// Log de inicialización
if (window.ModLogisticaConfig && window.ModLogisticaConfig.debug) {
    console.log('🎮 Handlers.js cargado - Manejadores de eventos inicializados');
}