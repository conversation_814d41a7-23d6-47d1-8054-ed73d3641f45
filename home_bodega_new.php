<?php
    // Forzar codificación UTF-8
    header('Content-Type: text/html; charset=UTF-8');

    // Desactivar todo tipo de caché
    header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
    header('Cache-Control: post-check=0, pre-check=0', false);
    header('Pragma: no-cache');




    require_once 'DatabaseConnection.php';

    try {
        session_start();
        $db = DatabaseConnection::getInstance();
        $conex = $db->getConnection();

        // Guardar usuario en sesión después del login exitoso
        if (isset($aux7)) { // RUT del usuario
            $_SESSION['usuario'] = $aux7;
        }

        register_shutdown_function(function() use ($db) {
            if ($db !== null) {
                $db->cleanup();
            }
        });

    } catch (Exception $e) {
        error_log("Error de conexión: " . $e->getMessage());
        die("Error de conexión: Por favor, contacte al administrador");
    }

    $today = date('Y-m-d');
    $thirtyDaysAgo = date('Y-m-d', strtotime('-30 days'));



    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);


    $sesion = $_GET['id_sesion'];


    // ALTERNATIVA PARA EL SESSION////////////////////////


    // ALTERNATIVA PARA EL SESSION////////////////////////

    $sql3 = "SELECT tut.nombre, tut.email , tla.RUT , tut.id
            , tut.PERFIL , tut2.id   as id_supervisor , tut.rut AS RUT_OR
            FROM TB_LOG_APP tla
            LEFT JOIN tb_user_tqw tut
            ON tla.RUT = tut.rut
            LEFT JOIN tb_user_tqw tut2
            ON tut2.email  = tut.correo_super
            WHERE TOKEN = '$sesion'";

    $result = mysqli_query($conex, $sql3);
    $row = mysqli_fetch_assoc($result);

    $nombre_user = $row['nombre'];
    $email = $row['email'];
    $perfil = $row['PERFIL'];
    $rut_ejecut = $row['RUT_OR'];
    $id_usuario = $row['id'];
    $id_supervisor = $row['id_supervisor'];
    /////////////////////////////////////////////////////
    $usuario = $row['RUT'];


    $solicitud_material = $conex->query(
        "
            SELECT TICKET, tut.Nombre_short, fecha , id_tecnico_traspaso
            , tut2.Nombre_short  as tecnicoDestino
            , CASE
                    WHEN FLAG_BODEGA  IS NOT NULL THEN 'OK'
                    ELSE '-'
                END AS ESTADO
                   FROM TB_LOGIS_TECNICO_SOLICITUD tlts
                   LEFT JOIN tb_user_tqw tut ON tut.id = tlts.tecnico
                   LEFT JOIN tb_user_tqw tut2  ON  tut2.id = tlts.id_tecnico_traspaso
                   WHERE
                    CASE
                        WHEN FLAG_BODEGA  IS NOT NULL THEN 'OK'
                        ELSE '-'
                    END = '-'
                   GROUP BY tlts.TICKET, tut.Nombre_short, fecha , tut2.Nombre_short
                   ORDER BY fecha  DESC
                "
    );

    // Verificar si la consulta fue exitosa
    if (!$solicitud_material) {
        if (function_exists('logSecurityEventGlobal')) {
            logSecurityEventGlobal('Database query failed in home_bodega_new.php', [
                'query' => 'solicitud_material',
                'error' => $conex->error,
                'file' => 'home_bodega_new.php',
                'line' => 76
            ], 'ERROR');
        }
        $solicitud_material = false;
    }



    $bd_notifica = $conex->query(
        "

        SELECT tut.Nombre_short
        , tut2.Nombre_short as Nombre2 ,
        A.serie  as  Serial
        ,tllbmt.serie as flag_bodega
        , A.`Fecha` as fecha_hora
        FROM  TB_LOGIS_TECNICO_SERIE_TRANSFIERE A
        LEFT JOIN tb_user_tqw tut  ON tut.id =  A.id_origen
        LEFT JOIN tb_user_tqw tut2 ON tut2.id =  A.id_destino
        LEFT JOIN TB_LOGIS_LOG_BODEGA_materialTecnico tllbmt  ON tllbmt.serie  = A.serie
        WHERE flagacepta = 'Si'
        AND flag_bodega is null
        ORDER BY A.fecha  desc
        LIMIT 50

    "
    );

    // Verificar si la consulta fue exitosa
    if (!$bd_notifica) {
        if (function_exists('logSecurityEventGlobal')) {
            logSecurityEventGlobal('Database query failed in home_bodega_new.php', [
                'query' => 'bd_notifica',
                'error' => $conex->error,
                'file' => 'home_bodega_new.php',
                'line' => 112
            ], 'ERROR');
        }
        $bd_notifica = false;
    }




    $sql_tecnicos = "SELECT DISTINCT Nombre_short
    FROM tb_user_tqw
    WHERE Nombre_short IS NOT NULL
    AND vigente = 'Si'
    AND `PERFIL` IN  ('TECNICO RESIDENCIAL','Supervisor Tecnico')
    ORDER BY Nombre_short
    ";
    $result_tecnicos = $conex->query($sql_tecnicos);

    // Verificar si la consulta fue exitosa
    if (!$result_tecnicos) {
        if (function_exists('logSecurityEventGlobal')) {
            logSecurityEventGlobal('Database query failed in home_bodega_new.php', [
                'query' => 'sql_tecnicos',
                'error' => $conex->error,
                'file' => 'home_bodega_new.php',
                'line' => 155
            ], 'ERROR');
        }
        $result_tecnicos = false;
    }




    // Verificar el estado de la conexión MySQL
    if ($conex->connect_error) {
        echo "<script>console.error('Error de conexión MySQL: " . addslashes($conex->connect_error) . "');</script>";
        echo '<div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i>
                Error de conexión a la base de datos. Por favor, contacte al administrador.
              </div>';
        $result23 = false;
    }
    
    // Obtener info del servidor MySQL para debugging
    $server_info = $conex->server_info;
    echo "<script>console.log('Conexión MySQL establecida. Versión: " . addslashes($server_info) . "');</script>";
    
    // Usar una consulta SELECT en lugar de SHOW TABLES para comprobar la vista
    $view_check = $conex->query("SELECT 1 FROM information_schema.TABLES WHERE TABLE_NAME = 'vw_logis_semaforo' AND TABLE_SCHEMA = DATABASE() LIMIT 1");
    if (!$view_check) {
        echo "<script>console.error('Error al verificar la vista: " . addslashes($conex->error) . "');</script>";
        echo '<div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i>
                Error al verificar la vista del semáforo.
              </div>';
        $result23 = false;
    } else if ($view_check->num_rows === 0) {
        // La vista no existe
        echo "<script>console.error('La vista VW_LOGIS_SEMAFORO no existe en la base de datos');</script>";
        echo '<div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle"></i>
                Error: Vista de semáforo no encontrada en la base de datos.
              </div>';
        $result23 = false;
    } else {
        // Consulta del semáforo con manejo de errores más detallado
        // Usar vw_logis_semaforo en minúsculas para evitar problemas de sensibilidad de mayúsculas/minúsculas
        $semaforo = "
        SELECT
        sem.*, turno.`Estado`,
        CASE WHEN Porcentaje > 0 OR SUM_JOB > 0 OR PorcentajeReversa > 0 THEN 1 ELSE 0 END AS Semaforo_gen
        FROM vw_logis_semaforo sem
        LEFT JOIN tb_user_tqw as usuu ON BINARY LOWER(usuu.`Nombre_short`) = BINARY LOWER(sem.`Nombre_Short`)
        LEFT JOIN (
            SELECT * FROM tb_turnos_py ttp
            WHERE DATE(STR_TO_DATE(FECHA , '%d/%m/%Y')) = DATE(NOW())
        ) AS turno ON BINARY LOWER(turno.rut) = BINARY LOWER(usuu.rut)
        ";
        
        // Verificar que podemos obtener al menos un registro - diagnóstico adicional
        $test_query = $conex->query("SELECT COUNT(*) AS total FROM vw_logis_semaforo");
        if ($test_query) {
            $row = $test_query->fetch_assoc();
            echo "<script>console.log('Total de registros en vw_logis_semaforo: " . $row['total'] . "');</script>";
        } else {
            echo "<script>console.error('Error al contar registros en vw_logis_semaforo: " . addslashes($conex->error) . "');</script>";
        }
    }

    // Añadir JavaScript para registrar el inicio de la consulta del semáforo
    echo "<script>console.log('Iniciando consulta del semáforo: " . date('H:i:s') . "');</script>";
    
    // Registrar la consulta SQL en modo debug (activado con #debug en la URL)
    if (isset($semaforo)) {
        echo "<script>
        if (window.location.hash === '#debug') {
            console.log('SQL de semáforo: ', `" . str_replace("\"", "\\'", $semaforo) . "`);
        }
        </script>";
    }

    try {
        // Solo ejecutar si la consulta está definida
        if (!isset($semaforo)) {
            echo "<script>console.log('Consulta de semáforo no definida, saltando ejecución');</script>";
            $result23 = false;
        } else {
            // Desactivar temporalmente el reporte de errores estricto
            $old_error_reporting = mysqli_report(MYSQLI_REPORT_OFF);
            
            $start_semaforo = microtime(true);
            $result23 = $conex->query($semaforo);
            $end_semaforo = microtime(true);
            $time_semaforo = ($end_semaforo - $start_semaforo) * 1000; // milisegundos

            // Registrar tiempo de ejecución en JavaScript
            echo "<script>console.log('Consulta del semáforo completada en " . round($time_semaforo, 2) . " ms');</script>";

            // Verificar si la consulta fue exitosa
            if (!$result23) {
                // Log del error para administradores
                if (function_exists('logSecurityEventGlobal')) {
                    logSecurityEventGlobal('Database query failed in home_bodega_new.php', [
                        'query' => 'semaforo_query',
                        'error' => $conex->error,
                        'file' => 'home_bodega_new.php',
                        'line' => 185
                    ], 'ERROR');
                }

                // Registrar error en JavaScript
                echo "<script>console.error('Error en consulta del semáforo: " . addslashes($conex->error) . "');</script>";

                // Mostrar mensaje amigable al usuario
                echo '<div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    Error al cargar los datos del semáforo. Por favor, recargue la página.
                  </div>';

                // Inicializar variables para evitar errores posteriores
                $result23 = false;
            } else {
                // Verificar si la consulta devolvió filas
                if ($result23->num_rows > 0) {
                    echo "<script>console.log('Consulta del semáforo exitosa: " . $result23->num_rows . " filas encontradas');</script>";
                } else {
                    echo "<script>console.log('Consulta del semáforo exitosa pero no se encontraron filas');</script>";
                }
            }
        } // Cerrar el bloque else
    } catch (Exception $e) {
        // Capturar cualquier excepción inesperada
        echo "<script>console.error('Excepción en consulta del semáforo: " . addslashes($e->getMessage()) . "');</script>";
        
        // Mostrar mensaje amigable al usuario
        echo '<div class="alert alert-warning">
            <i class="bi bi-exclamation-triangle"></i>
            Error al procesar los datos del semáforo. Por favor, recargue la página.
          </div>';
          
        // Inicializar variables para evitar errores posteriores
        $result23 = false;
    }

    $zero_pendientes_count = 0;
    $non_zero_count = 0;
    $zero_sum_job_count = 0;
    $non_zero_sum_job_count = 0;
    $zero_pendientes_reversa_count = 0;
    $non_zero_pendientes_reversa_count = 0;
    $supervisores = [];
    $cantidades = [];
    $supervisorCountsPendientes = [];
    $supervisorCountsSumJob = [];
    $supervisorCountsReversa = [];


?>


<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PORTAL OPERACIONES TELQWAY</title>
    <!-- Bootstrap CSS desde CDN -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/animations.css">
    <link rel="stylesheet" href="css/sidebar.css">
    <link rel="stylesheet" href="css/app_grid.css">
    <link rel="stylesheet" href="css/tables.css">
    <link rel="stylesheet" href="css/expcard.css">
    <title>Diseño Solicitado</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="icon" type="image/png" href="img/icons/logo.png">
    <link rel="stylesheet" href="css/home_bodega_new.css">
    <!-- jQuery debe ir antes de Select2 -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap Bundle JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Select2 CSS y JS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    
    <!-- Sistema de lazy loading -->
    <script src="js/lazy-loading.js"></script>
    
    <!-- Script de compatibilidad para lazy loading -->
    <script src="js/lazy-loading-compatibility.js"></script>
    
    <script>
    // Script para monitorear la carga de la página y el semáforo
    console.log('Iniciando carga de home_bodega_new.php: ' + new Date().toLocaleTimeString());
    
    // Función para registrar mensajes de error
    function logError(message, details) {
        console.error('ERROR: ' + message, details);
        // También podemos mostrar una notificación visual si es necesario
    }
    
    // Cuando el DOM esté listo
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM completamente cargado: ' + new Date().toLocaleTimeString());
        
        // Intentar capturar errores 404 en los recursos
        window.addEventListener('error', function(e) {
            if (e.target && e.target.tagName) {
                const element = e.target;
                if ((element.tagName === 'SCRIPT' || element.tagName === 'LINK') && 
                    e.target.src && e.target.src.includes('lazy-loading.js')) {
                    console.error('Error al cargar recurso: ' + e.target.src);
                    console.error('Esto podría estar afectando la funcionalidad del semáforo');
                }
            }
        }, true);
    });
    </script>

    <style>
        /* Estilos para el modal de solicitudes */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            background-color: white;
            border-radius: 15px;
            width: 98%;
            max-width: 1400px;
            max-height: 85vh;
            box-shadow: 0 5px 25px rgba(0, 0, 0, 0.4);
            animation: fadeIn 0.4s ease;
            transform: scale(1);
            transition: transform 0.3s ease;
            display: flex;
            flex-direction: column;
        }

        .modal-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-top-left-radius: 15px;
            border-top-right-radius: 15px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            margin: 0;
            color: #495057;
            font-weight: 600;
            font-size: 1.25rem;
        }

        .modal-body {
            padding: 25px;
            overflow-y: auto;
            flex: 1;
        }

        .modal-footer {
            padding: 15px 20px;
            border-top: 1px solid #dee2e6;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .btn-close {
            background: transparent;
            border: none;
            font-size: 1.5rem;
            line-height: 1;
            color: #6c757d;
            cursor: pointer;
            transition: color 0.15s ease;
            padding: 0;
            margin: 0;
        }

        .btn-close:hover {
            color: #343a40;
        }

        .btn-confirm, .btn-cancel {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-confirm {
            background-color: #28a745;
            color: white;
        }

        .btn-confirm:hover {
            background-color: #218838;
            transform: scale(1.05);
        }

        .btn-cancel {
            background-color: #dc3545;
            color: white;
        }

        .btn-cancel:hover {
            background-color: #c82333;
            transform: scale(1.05);
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        /* Estilos para la tabla dentro del modal */
        #solicitudesContenido table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            font-size: 14px;
        }

        #solicitudesContenido table th,
        #solicitudesContenido table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }

        #solicitudesContenido table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        #solicitudesContenido table tr:hover {
            background-color: #f5f5f5;
        }

        #solicitudesContenido .search-container {
            margin-bottom: 15px;
        }

        #solicitudesContenido .search-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }

        #solicitudesContenido .search-input:focus {
            border-color: #80bdff;
            outline: 0;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
    </style>

</head>

<body>


    <?php include('header_supervisor.php'); ?>



    <div id="appGrid" class="app-grid">
        <div class="app-item" data-bs-toggle="offcanvas" data-bs-target="#offcanvasrevSuper">
            <div class="app-icon"><i class="fas fa-users"></i></div>
            <span class="app-name">Usuarios</span>
        </div>

        <?php if($id_usuario == 17) { ?>
        <div class="app-item" id="mejorasApp">
            <div class="app-icon"><i class="fas fa-brain"></i></div>
            <span class="app-name">Mejoras APP</span>
        </div>

        <?php } ?>


        <?php if($id_usuario == 303) { ?>
        <div class="app-item">
            <a href="Tecnico_Home_LOGIS_TEST.php?id_sesion=<?php echo $sesion; ?>">
                <div class="app-icon"><i class="fas fa-user"></i></div>
                <span class="app-name">Usuario Logistica</span>
            </a>
        </div>
        <?php } ?>

        <div class="app-item" data-bs-toggle="offcanvas" data-bs-target="#soporteTecnico">
            <div class="app-icon"><i class="bi bi-headset"></i></div>
            <span class="app-name">Soporte Tecnico</span>
        </div>

    </div>

    <!-- Sidebar Toggle Button -->
    <button class="sidebar-toggle btn btn-link" aria-label="Toggle Sidebar">
        <i class="bi bi-list toggle-icon"></i>
    </button>

    <!-- Sidebar -->
    <aside class="sidebar">
        <div class="sidebar-brand">
            <img src="img/icons/logo.png" alt="Logo" style=" display: block; margin: 0 auto;">

        </div>
        <div class="menu-section">

            <ul class="sidebar-menu">
                <li>
                    <div class="menu-item" data-section="Home" data-toggle="submenu">
                        <i class="bi bi-house"></i>
                        <span data-i18n="nav.Home">Home</span>
                    </div>

                </li>
                <li>
                    <div class="menu-item" data-toggle="submenu">
                        <i class="bi bi-speedometer2"></i>
                        <span data-i18n="nav.dashboards">Dashboards</span>
                        <i class="bi bi-chevron-down submenu-arrow"></i>
                        <span class="badge rounded-pill" style="margin-left: 8px;">5</span>
                    </div>
                    <ul class="submenu">
                        <li><a class="menu-item" data-section="analytics" data-i18n="nav.analytics">Pool logistica</a>
                        </li>
                        <li><a class="menu-item" data-section="ecommerce" data-i18n="nav.ecommerce">Cierre
                                inventario</a></li>

                        <!-- <li><a class="menu-item" data-section="crm" data-i18n="nav.crm">Logistica</a></li>
                        <li><a class="menu-item" data-section="projects" data-i18n="nav.projects">Reporte OT digital</a>
                        </li>
                        <li><a class="menu-item" data-section="marketing" data-i18n="nav.marketing">SME</a></li> -->
                    </ul>
                </li>


                <li>
                <div class="menu-item" data-toggle="submenu">
                        <i class="bi bi-speedometer2"></i>
                        <span data-i18n="nav.dashboards">Cierres inventarios</span>
                        <i class="bi bi-chevron-down submenu-arrow"></i>
                        <span class="badge rounded-pill" style="margin-left: 8px;">5</span>
                    </div>
                    <ul class="submenu">
                    <li>
    <a class="menu-item"
       href="Bodega_Cierre_inventarioIX.php?id_sesion=<?php echo htmlspecialchars($sesion); ?>"
       onclick="handleClick(event)"
       data-href="Bodega_Cierre_inventarioIX.php?id_sesion=<?php echo htmlspecialchars($sesion); ?>">
        <i class="bi bi-box-seam"></i>
        <span>Modulo Cierre Inventario</span>
    </a>
</li>


<script>
    // Add this to your existing JavaScript
function handleClick(event) {
    event.preventDefault();
    const link = event.currentTarget;
    console.log('Link clicked');
    console.log('Attempting to navigate to:', link.getAttribute('data-href'));

    // Force navigation after logging
    window.location.href = link.getAttribute('data-href');
}
</script>
                        <li><a class="menu-item" data-section="mod_inventario" data-i18n="nav.mod_inventario">Resumen Cierre Inventario</a></li>
                        <li><a class="menu-item" data-section="pool_inventario" data-i18n="nav.pool_inventario">Pool Cierre Inventario</a></li>

                    </ul>
                </li>

                <li>
                    <div class="menu-item" data-section="cubos" data-toggle="submenu">
                        <i class="bi bi-graph-up"></i>
                        <span data-i18n="nav.cubos">Cubo datos</span>
                        <i class="bi bi-chart submenu-arrow"></i>
                    </div>
                </li>

            </ul>
        </div>
    </aside>

    <!-- Content -->
    <div class="content">
        <!-- Content Containers -->
        <div class="content-containers">

        <div id="Home-container" class="section-container" style="display: block;">


            <!-- Header con Bootstrap -->
            <div class="row mb-5">
                <!-- Última actualización Oracle -->
                <div class="col-lg-3 col-md-6">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body d-flex align-items-center justify-content-center">
                            <span class="kpi-icon icon1 me-3"></span>
                            <div class="text-center">
                                <h6 class="card-title mb-1">Última actualización Oracle</h6>
                                <p class="card-text fw-bold mb-0">
                                    <span id="fecha_carga_span"></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Última actualización semaforo -->
                <div class="col-lg-3 col-md-6">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body d-flex align-items-center justify-content-center">
                            <span class="kpi-icon icon1 me-3"></span>
                            <div class="text-center">
                                <h6 class="card-title mb-1">Última actualización semaforo</h6>
                                <p class="card-text fw-bold mb-0">
                                    <span
                                        id="fecha_carga_span2"><?php date_default_timezone_set('America/Santiago'); echo date('Y-m-d H:i:s'); ?></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Último gestión en APP -->
                <div class="col-lg-3 col-md-6">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body d-flex align-items-center justify-content-center">
                            <span class="kpi-icon icon1 me-3"></span>
                            <div class="text-center">
                                <h6 class="card-title mb-1">Último gestión en APP</h6>
                                <p class="card-text fw-bold mb-0">
                                    <span id="fecha_carga_span3"></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Último reclamo en APP -->
                <div class="col-lg-3 col-md-6">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body d-flex align-items-center justify-content-center">
                            <span class="kpi-icon icon_warning me-3"></span>
                            <div class="text-center">
                                <h6 class="card-title mb-1">Último reclamo en APP</h6>
                                <p class="card-text fw-bold mb-0">
                                    <span id="fecha_carga_span4"></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-4" id="tablesContainer">
                <div class="col-12">
                    <h4>TABLA LOGÍSTICA</h4>
                    <!-- Contenedor de tabla con funciones de búsqueda y ordenamiento -->
                    <div class="table-wrapper">
                        <!-- Campo de búsqueda para la tabla -->
                        <div class="input-group mb-3">
                            <span class="input-group-text"><i class="bi bi-search"></i></span>
                            <input type="text" id="filtroTablaLogistica" class="form-control" placeholder="Buscar en la tabla...">
                        </div>
                        <div style="max-height: 400px; overflow-y: auto;">
                            <table id="tablaLogistica" class="tabla-styled" style="border-spacing: 0; border: 1px solid #d3d3d3; width: 100%;">
                                <?php
                                // Generar los encabezados de la tabla
                                echo '<thead><tr>';
                                $headers = ['Nombre', 'Vigente','Estado', 'Org', 'Supervisor'
                                , 'Semaforo general', 'Directa recepcionados'
                                , 'Directa por recepcionar', 'Directa', 'Series sin gestión'
                                , 'Sin gestión', 'Equipos Devueltos', 'Equipos por devolver','transito Reversa', 'Status Reversa'];
                                foreach ($headers as $index => $header) {
                                    echo '<th style="font-size: 12px;">
                                            ' . htmlspecialchars($header) . '
                                            <i class="bi bi-arrow-down-up sort-icon" data-column="' . $index . '"></i>
                                        </th>';
                                }
                                echo '</tr>';

                                echo '</thead>';
                                echo '<tbody>';


                                $supervisorStats = [];

                                // Ingresar al bucle para procesar cada fila de la consulta
                                if ($result23 && $result23->num_rows > 0) {
                                    while ($fila = $result23->fetch_assoc()) {
                                    // Actualizar PendientesCount
                                    // Actualizar PendientesCount
                                    if (isset($fila['PendientesCount']) && $fila['vigente'] == 'Si') {
                                        if ($fila['PendientesCount'] == 0) {
                                            $zero_pendientes_count++;
                                        } else {
                                            $non_zero_count++;
                                        }
                                    }

                                    // Actualizar SUM_JOB
                                    if (isset($fila['SUM_JOB']) && $fila['vigente'] == 'Si') {
                                        if ($fila['SUM_JOB'] == 0) {
                                            $zero_sum_job_count++;
                                        } else {
                                            $non_zero_sum_job_count++;
                                        }
                                    }

                                    // Actualizar PendientesCountReversa
                                    if (isset($fila['PendientesCountReversa']) && $fila['vigente'] == 'Si') {
                                        if ($fila['PendientesCountReversa'] == 0) {
                                            $zero_pendientes_reversa_count++;
                                        } else {
                                            $non_zero_pendientes_reversa_count++;
                                        }
                                    }


                                    if ($fila['vigente'] == 'Si') {
                                        $supervisor = $fila['supervisor'];

                                        // Inicializar estructura si no existe
                                        if (!isset($supervisorStats[$supervisor])) {
                                            $supervisorStats[$supervisor] = [
                                                'pendientes' => 0,
                                                'gestion' => 0,
                                                'reversa' => 0
                                            ];
                                        }

                                        // Contabilizar técnicos con pendientes
                                        if ($fila['PendientesCount'] > 0) {
                                            $supervisorStats[$supervisor]['pendientes']++;
                                        }

                                        // Contabilizar técnicos con SUM_JOB
                                        if ($fila['SUM_JOB'] > 0) {
                                            $supervisorStats[$supervisor]['gestion']++;
                                        }

                                        // Contabilizar técnicos con pendientes de reversa
                                        if ($fila['PendientesCountReversa'] > 0) {
                                            $supervisorStats[$supervisor]['reversa']++;
                                        }
                                    }


                                    $supervisores = array_keys($supervisorStats);
                                    $pendientes = array_column($supervisorStats, 'pendientes');
                                    $gestion = array_column($supervisorStats, 'gestion');
                                    $reversa = array_column($supervisorStats, 'reversa');


                                // Renderizar la fila de la tabla
                                echo '<tr>';
                                echo '<td style="font-size: 10px; text-align: center; text-decoration: underline; text-decoration-color: blue;">' . htmlspecialchars($fila['Nombre_Short']) . '</td>';
                                echo '<td style="font-size: 10px; text-align: center;">' . htmlspecialchars($fila['vigente']) . '</td>';
                                echo '<input type="hidden" class="rut-value" value="' . htmlspecialchars($fila['rut']) . '">';
                                echo '<td style="font-size: 10px; text-align: center;">' . htmlspecialchars($fila['Estado']) . '</td>';
                                echo '<td style="font-size: 10px; text-align: center;">' . htmlspecialchars($fila['Org']) . '</td>';

                                // Assuming $supervisor contains the value you want to evaluate
                                $supervisor = htmlspecialchars($fila['supervisor']);
                                $customClass = '';
                                switch ($supervisor) {
                                    case 'ARIAS':
                                        $customClass = 'custom-supervisor-arias';
                                        break;
                                    case 'CORROTEA':
                                        $customClass = 'custom-supervisor-corrotea';
                                        break;
                                    case 'BARRERA':
                                        $customClass = 'custom-supervisor-barrera';
                                        break;
                                    case 'GOMEZ':
                                        $customClass = 'custom-supervisor-gomez';
                                        break;
                                    case 'GUERRERO':
                                        $customClass = 'custom-supervisor-guerrero';
                                        break;
                                    default:
                                        $customClass = ''; // Default class if none of the cases match
                                        break;
                                }
                                echo '<td class="' . $customClass . '" style="font-size: 10px; text-align: center;">' . $supervisor . '</td>';

                                echo '<td style="text-align: center;">' .
                                    ($fila['Semaforo_gen'] == '1'
                                        ? '<span style="display:inline-block; width:11.52px; height:11.52px; background-color:red; border-radius:50%;"></span>'
                                        : '<span style="display:inline-block; width:11.52px; height:11.52px; background-color:green; border-radius:50%;"></span>')
                                    . '</td>';
                                    echo '<td style="font-size: 10px; text-align: center; background-color: #e8f5e9;">' . htmlspecialchars($fila['Gestionados']) . '</td>';
                                    echo '<td style="font-size: 10px; text-align: center; background-color: #e8f5e9;">' . htmlspecialchars($fila['PendientesCount']) . '</td>';
                                    echo '<td style="text-align: center; background-color: #e8f5e9;">' .
                                        ($fila['Porcentaje'] == '0'
                                            ? '<span style="display:inline-block; width:11.52px; height:11.52px; background-color:green; border-radius:50%;"></span>'
                                            : '<span style="display:inline-block; width:11.52px; height:11.52px; background-color:red; border-radius:50%;"></span>')
                                        . '</td>';

                                echo '<td style="font-size: 10px; text-align: center; background-color: #e3f2fd;">' . htmlspecialchars($fila['SUM_JOB']) . '</td>';
                                echo '<td style="text-align: center; background-color: #e3f2fd;">' .
                                    ($fila['SUM_JOB'] == '0'
                                        ? '<span style="display:inline-block; width:11.52px; height:11.52px; background-color:green; border-radius:50%;"></span>'
                                        : '<span style="display:inline-block; width:11.52px; height:11.52px; background-color:red; border-radius:50%;"></span>')
                                    . '</td>';
                                echo '<td style="font-size: 10px; text-align: center; background-color: #ffe0b2;">' . htmlspecialchars($fila['GestionadosReversa']) . '</td>';
                                echo '<td style="font-size: 10px; text-align: center; background-color: #ffe0b2;">' . htmlspecialchars($fila['PendientesCountReversa']) . '</td>';
                                echo '<td style="font-size: 10px; text-align: center; background-color: #ffe0b2;">' . htmlspecialchars($fila['transitoReversa']) . '</td>';
                                echo '<td style="text-align: center; background-color: #ffe0b2;">' .
                                    ($fila['PendientesCountReversa'] > '0'
                                        ? '<span style="display:inline-block; width:11.52px; height:11.52px; background-color:red; border-radius:50%;"></span>'
                                        : ($fila['transitoReversa'] > '0'
                                            ? '<span style="display:inline-block; width:11.52px; height:11.52px; background-color:#FFB347; border-radius:50%;"></span>'
                                                    : '<span style="display:inline-block; width:11.52px; height:11.52px; background-color:green; border-radius:50%;"></span>'
                                        ))
                                    . '</td>';
                                echo '</tr>';
                                    }
                                } else {
                                    // Si no hay datos o la consulta falló, mostrar mensaje
                                    echo '<tr><td colspan="12" class="text-center">
                                            <div class="alert alert-info">
                                                <i class="bi bi-info-circle"></i>
                                                No hay datos disponibles en este momento.
                                            </div>
                                          </td></tr>';
                                }

                                echo '</tbody>';
                                ?>
                            </table>
                        </div>

                        <!-- Contenedor para resultados de búsqueda -->
                        <div id="resultContainer" class="result-container" style="display: none;">
                            <div class="result-header">
                                <button id="closeResultContainer" class="close-result" style="float: right;">Close</button>
                            </div>
                            <div class="result-content">
                                <!-- Los resultados se cargarán aquí -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <div id="analytics-container" class="section-container" style="display: none;">
            <!-- Añadir botones para controlar la visibilidad de las tablas -->
            <div class="mb-4 pt-3">
                <div class="d-flex flex-wrap justify-content-center gap-2">
                    <!-- Modificar el botón de solicitudes para incluir el badge -->
                    <!-- Botón switch principal para mostrar/ocultar todos -->
                    <button id="toggleAll" class="btn btn-success toggle-all active mb-2 w-100">
                        <i class="bi bi-toggle-on"></i> Mostrar/Ocultar Todo
                    </button>

                    <button class="btn btn-primary toggle-table active" data-target="solicitud-container">
                        <i class="bi bi-box-seam"></i> Solicitudes - Transferencia
                        <span class="notification-badge" id="solicitud-badge" style="display: none">0</span>
                    </button>

                    <!-- Añadir badge al botón de Pool Directa -->
                    <button class="btn btn-primary toggle-table active" data-target="directa-container">
                        <i class="bi bi-arrow-down-up"></i> Pool Directa
                        <span class="notification-badge" id="directa-badge" style="display: none">0</span>
                    </button>

                    <!-- Añadir badge al botón de Equipos -->
                    <button class="btn btn-primary toggle-table active" data-target="equipos-container">
                        <i class="bi bi-laptop"></i> Equipos a VTR
                        <span class="notification-badge" id="equipos-badge" style="display: none">0</span>
                    </button>

                    <!-- Añadir badge al botón de Pool Reversa -->
                    <button class="btn btn-primary toggle-table active" data-target="reversa-container">
                        <i class="bi bi-arrow-counterclockwise"></i> Pool Reversa
                        <span class="notification-badge" id="reversa-badge" style="display: none">0</span>
                    </button>

                    <!-- Añadir badge al botón de Reversa Aceptada -->
                    <button class="btn btn-primary toggle-table active" data-target="aceptada-container">
                        <i class="bi bi-check-circle"></i> Reversa Aceptada
                        <span class="notification-badge" id="aceptada-badge" style="display: none">0</span>
                    </button>
                </div>
            </div>



            <div class="container-fluid mb-4">
                <div id="solicitud-container">
                    <div class="row">
                        <!-- Contenedor izquierdo con la tabla de solicitudes -->
                        <div class="col-md-6">
                            <h4>solicitudes y asignación de material</h4>
                            <div class="input-group mb-3">
                                <span class="input-group-text"><i class="bi bi-search"></i></span>
                                <input type="text" id="searchInputtablaSolicitud1" class="form-control" placeholder="Buscar en la tabla...">
                                <button type="button" class="btn btn-danger" id="clearSearchInput">
                                    <i class="bi bi-x-circle"></i>
                                </button>
                                <select class="form-control" style="width: auto;" onchange="insertText(this.value, 'tablaSolicitud1')">
                                    <option value="">Seleccione una opción</option>
                                    <option value="Corrotea">Corrotea</option>
                                    <option value="Jimenez">Jimenez</option>
                                </select>
                                <button type="button" class="btn btn-primary" id="btnHistorial" onclick="abrirModalSolicitudes()">
                                    <i class="bi bi-clock-history"></i>
                                </button>
                            </div>
                            <div style="max-height: 400px; overflow-y: auto;">
                                <table id="tablaSolicitud1" class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Fecha</th>
                                            <th>Desde</th>
                                            <th>Hacia</th>
                                            <th>ESTADO</th>
                                            <th>Revisar</th>
                                            <th>Terminar</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if ($solicitud_material && $solicitud_material->num_rows > 0): ?>
                                            <?php while ($fila = mysqli_fetch_assoc($solicitud_material)): ?>
                                                <tr>
                                                    <td class="button-cell"><?php echo $fila['fecha']; ?></td>
                                                    <td class="button-cell"><?php echo $fila['Nombre_short']; ?></td>
                                                    <td class="button-cell"><?php echo $fila['tecnicoDestino']; ?></td>
                                                    <td class="button-cell"><?php echo $fila['ESTADO']; ?></td>
                                                    <td class="button-cell">
                                                        <button type="button" class="btn-warning" onclick="hacerSolicitudAjax('<?php echo $fila['TICKET']; ?>')">
                                                            <i class="bi bi-eye-fill"></i>
                                                        </button>
                                                    </td>
                                                    <td class="button-cell">
                                                        <?php if ($fila['ESTADO'] != "OK"): ?>
                                                            <button type="button" class="btn-success" onclick="solicitudOK('<?php echo $fila['TICKET']; ?>', '<?php echo $id_usuario; ?>', this)">
                                                                <i class="bi bi-check-circle-fill"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endwhile; ?>
                                        <?php else: ?>
                                            <tr>
                                                <td colspan="6" class="text-center">
                                                    <div class="alert alert-info">
                                                        <i class="bi bi-info-circle"></i>
                                                        No hay solicitudes de material pendientes.
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <div class="col-md-6 panel-container">
                            <!-- Contenedor de la tabla de transferencia - visible por defecto -->
                            <div id="transferencia-container">
                                <h4>Pool de transferencia entre tecnicos</h4>
                                <div class="input-group mb-3">
                                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                                    <input type="text" id="filtroPoolTransferencia" class="form-control" placeholder="Buscar en la tabla...">
                                </div>
                                <div style="max-height: 400px; overflow-y: auto;">
                                    <table id="poolNotificacion" class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Serial</th>
                                                <th>Fecha</th>
                                                <th>Tecnico1</th>
                                                <th>Tecnico2</th>
                                                <th>Historial</th>
                                                <th>Cerrar</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($bd_notifica && $bd_notifica->num_rows > 0): ?>
                                                <?php while ($fila = mysqli_fetch_assoc($bd_notifica)): ?>
                                                    <tr>
                                                        <td><?php echo $fila['Serial']; ?></td>
                                                        <td><?php echo $fila['fecha_hora']; ?></td>
                                                        <td><?php echo $fila['Nombre_short']; ?></td>
                                                        <td><?php echo $fila['Nombre2']; ?></td>
                                                        <td class="button-cell">
                                                            <button type="button" class="btn-warning"
                                                                    onclick="redirigirEnTransferencia('<?php echo $fila['Serial']; ?>', '<?php echo $fila['Serial']; ?>', <?php echo $id_usuario; ?>, 'VER')">
                                                                <i class="bi bi-eye-fill"></i>
                                                            </button>
                                                        </td>
                                                        <td class="button-cell">
                                                            <?php if ($fila['flag_bodega'] === null): ?>
                                                                <button type="button" class="btn-success"
                                                                        onclick="solicitudOK_tecnico('<?php echo $fila['Serial']; ?>', '<?php echo $id_usuario; ?>', this)">
                                                                    <i class="bi bi-house-add-fill"></i>
                                                                </button>
                                                            <?php endif; ?>
                                                        </td>
                                                    </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="5" class="text-center">
                                                        <div class="alert alert-info">
                                                            <i class="bi bi-info-circle"></i>
                                                            No hay transferencias pendientes.
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Panel de detalles de solicitud - inicialmente oculto -->
                            <div id="tablaSolicitudx">
                                <!-- El contenido se generará dinámicamente con JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mb-4" id="directa-container">
                    <div class="col-12">
                        <h4>POOL DIRECTA</h4>
                        <!-- Campo de búsqueda para la primera tabla -->
                        <div class="input-group mb-3">
                            <span class="input-group-text"><i class="bi bi-search"></i></span>
                            <input type="text" id="filtroTablaMovimientos" class="form-control" placeholder="Buscar en la tabla...">
                        </div>
                        <div style="max-height: 400px; overflow-y: auto;">
                            <table id="TablaMovimientos" class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Serial</th>
                                        <th>Técnico</th>
                                        <th>Fecha</th>
                                        <th>Motivo</th>
                                        <th>Historial</th>
                                        <th>A VTR</th>
                                        <th>Confirma</th>
                                        <th>Rechaza</th>
                                        <th>Cerrar</th>
                                    </tr>
                                </thead>
                                <tbody>


                                    <?php
                                        $query = "
                                            SELECT
                                                RZ.Serial,
                                                RZ.Item,
                                                RZ.State,
                                                RZ.Status,
                                                RZ.`Receipt Date`,
                                                tut.Nombre_short,
                                                A.fecha_hora,
                                                A.id_tecnico_origen,
                                                A.id_tecnico_destino,
                                                A.id_movimiento,
                                                A.motivo,
                                                A.ticket,
                                                tut.id,
                                                tut_super.nombre as Super,
                                                A.id as ult_id
                                            FROM TB_FERRET_DIRECTA1 RZ
                                            LEFT JOIN tb_logis_movimientos A ON RZ.`Unit Number` = A.id
                                            LEFT JOIN TP_LOGIS_MOVIMIENTOS tlm ON A.id_movimiento = tlm.id
                                            LEFT JOIN tb_user_tqw tut ON tut.rut = RZ.Subinventory
                                            LEFT JOIN tb_user_tqw tut_super ON tut_super.id = A.id_tecnico_origen
                                            WHERE A.id_movimiento IN (2,3,4,15,19)
                                            AND id_tecnico_destino = " . $id_usuario . "
                                            AND motivo != 'FALLO ELECTRONICO'
                                            AND State != 'Issued out of stores'
                                            ORDER BY fecha_hora DESC";

                                        $resultado = $conex->query($query);

                                        // Verificar si la consulta fue exitosa
                                        if ($resultado && $resultado->num_rows > 0) {
                                            while ($row = mysqli_fetch_assoc($resultado)) {
                                            echo '<tr>';
                                            echo '<td>' . $row['Serial'] . '</td>';
                                            echo '<td>' . $row['Nombre_short'] . '</td>';
                                            echo '<td>' . $row['fecha_hora'] . '</td>';
                                            echo '<td>' . $row['motivo'] . '</td>';
                                            echo '<td class="button-cell"><button type="button" class="btn-warning" onclick="redirigirEnTransferencia(\'' . $row['Serial'] . '\', \'' . $row['Item'] . '\', ' . $id_usuario . ', \'VER\')"><i class="bi bi-eye-fill"></i></button></td>';

                                            echo '<td class="button-cell"><button type="button" class="btn-light" onclick="rechazoMaterial(\'' . $row['Serial'] . '\', \'' . $row['Item'] . '\', ' . $row['id_tecnico_origen'] . ', \'VTR\')"><i class="bi bi-journal-x"></i> </button></td>';

                                            if ($row['id_movimiento'] == 2 || $row['id_movimiento'] == 3) {
                                                echo '<td class="button-cell"><button type="button" class="btn-success" onclick="redirigirEnTransferencia(\'' . $row['Serial'] . '\', \'' . $row['ult_id'] . '\', \'' . $row['Super'] . '\', \'CONFIRMA\')"><i class="bi bi-info-circle"></i> </button></td>';
                                            } else {
                                                echo '<td class="button-cell"><button type="button" class="btn-success" onclick="redirigirEnTransferencia(\'' . $row['Serial'] . '\', \'' . $row['ticket'] . '\', \'' . $row['Super'] . '\', \'ACEPTA JUSTIFICA\')"><i class="bi bi-info-circle"></i> </button></td>';
                                            }
                                            if ($row['id_movimiento'] == 2) {
                                                echo '<td class="button-cell"><button type="button" class="btn-danger" onclick="rechazoMaterial(\'' . $row['Serial'] . '\', \'' . $row['Item'] . '\', \'' . $row['id_tecnico_origen'] . '\', \'rechaza_bodega\')"><i class="bi bi-journal-x"></i></button></td>';
                                            } else {
                                                echo '<td class="button-cell"><button type="button" class="btn-danger" onclick="rechazoMaterial(\'' . $row['Serial'] . '\', \'' . $row['ticket'] . '\', \'' . $row['ticket'] . '\', \'rechaza_bodega_justifi\')"><i class="bi bi-journal-x"></i></button></td>';
                                            }
                                            echo '<td class="button-cell">';
                                            echo '<button type="button" class="btn-secondary" onclick="cerrarFlujo(\'' . $row['ult_id'] . '\')"><i class="bi bi-x-circle"></i></button>';
                                            echo '</td>';
                                            echo '</tr>';
                                            }
                                        } else {
                                            echo '<tr><td colspan="8" class="text-center">
                                                    <div class="alert alert-info">
                                                        <i class="bi bi-info-circle"></i>
                                                        No hay materiales pendientes.
                                                    </div>
                                                  </td></tr>';
                                        }
                                    ?>

                                </tbody>
                            </table>
                        </div> <!-- Cierre del div con overflow -->
                    </div> <!-- Cierre del col-6 -->

                </div>

                <!-- Segunda tabla (ocupa el 100% del ancho) -->
                <div class="row mb-4" id="equipos-container">
                    <div class="col-12">
                        <h4>Movimientos de Equipos</h4>
                        <!-- Campo de búsqueda para la segunda tabla -->
                        <div class="input-group mb-3">
                            <span class="input-group-text"><i class="bi bi-search"></i></span>
                            <input type="text" id="filtroTablaDirectaVTR" class="form-control" placeholder="Buscar en la tabla...">
                        </div>
                        <div style="max-height: 400px; overflow-y: auto;">
                            <table id="TabladirectaVTR" class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Serial</th>
                                        <th>Tecnico</th>
                                        <th>Fecha</th>
                                        <th>Historial</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php

                                    $query_vtr = "
                                    SELECT RZ.Serial
                                    , RZ.Item
                                    , RZ.Org
                                    , RZ.Subinventory
                                    , RZ.`Locator`
                                    , RZ.State
                                    , RZ.Status
                                    , RZ.`Receipt Date`
                                    , fecha_hora, serie,
                                    id_tecnico_origen
                                    , id_tecnico_destino
                                    , observacion
                                    , id_movimiento
                                    , motivo, ticket , tut.Nombre_short
                                    , tfd.descripcion
                                    , tfd.familia
                                    FROM
                                    TB_FERRET_DIRECTA1 RZ
                                    LEFT JOIN
                                    tb_logis_movimientos A
                                    ON RZ.`Unit Number` = A.id
                                    LEFT JOIN TP_LOGIS_MOVIMIENTOS tlm
                                    ON A.id_movimiento = tlm.id
                                    LEFT JOIN
                                    tb_user_tqw tut
                                    ON
                                    tut.rut = RZ.Subinventory
                                    LEFT JOIN tb_user_tqw tut_destino
                                    ON tut_destino.id = A.id_tecnico_destino
                                    LEFT JOIN tp_ferret_desc tfd
                                    ON RZ.Item = tfd.ID
                                    WHERE A.id_movimiento = 13
                                    AND State <> 'Issued out of stores'";

                                    $bd_VTR = $conex->query($query_vtr);

                                    // Verificar si la consulta fue exitosa
                                    if ($bd_VTR && $bd_VTR->num_rows > 0) {
                                        while ($fila = mysqli_fetch_assoc($bd_VTR)) {
                                        echo '<tr>';
                                            echo '<td class="button-cell">' . $fila['Serial'] . '</td>';
                                            echo '<td class="button-cell">' . $fila['Nombre_short'] . '</td>';
                                            echo '<td class="button-cell">' . $fila['fecha_hora'] . '</td>';
                                            echo '<td class="button-cell">
                                                <button type="button" class="btn-warning"
                                                    onclick="redirigirEnTransferencia(\'' . $fila['Serial'] . '\', \'' . $fila['Item'] . '\', ' . $id_usuario . ', \'VER\')">
                                                    <i class="bi bi-eye-fill"></i>
                                                </button>
                                            </td>';
                                            echo '</tr>';
                                        }
                                    } else {
                                        echo '<tr><td colspan="4" class="text-center">
                                                <div class="alert alert-info">
                                                    <i class="bi bi-info-circle"></i>
                                                    No hay materiales VTR disponibles.
                                                </div>
                                              </td></tr>';
                                    }
                                    ?>

                                </tbody>
                            </table>
                        </div> <!-- Close second column -->
                        <!-- Close the flex container -->
                    </div> <!-- col-6 -->
                </div>

                    <!-- Tercera tabla (ocupa el 100% del ancho) -->
                <div class="row mb-4" id="reversa-container">
                    <div class="col-12">
                        <h4>Pool Reversa</h4>
                        <!-- Campo de búsqueda para la tercera tabla -->
                        <div class="input-group mb-3">
                            <span class="input-group-text"><i class="bi bi-search"></i></span>
                            <input type="text" id="filtroPoolReversa" class="form-control" placeholder="Buscar en la tabla...">
                        </div>
                        <div style="max-height: 400px; overflow-y: auto; overflow-x: auto; width: 100%;">
                            <table id="PoolReversa" class="table table-striped" style="width: 100%;">
                                <thead>
                                    <tr>
                                        <th>Serial</th>
                                        <th>Tecnico</th>
                                        <th>Org</th>
                                        <th>Subinventory</th>
                                        <th>Motivo</th>
                                        <th>Fecha</th>
                                        <th>Historial</th>
                                        <th>Confirmar</th>
                                        <th>Rechazar</th>
                                    </tr>
                                </thead>
                                <tbody>

                                <?php
                                    $query_reversa = "
                                    SELECT RZ.Serial
                                    , RZ.Item
                                    , RZ.Org
                                    , RZ.Subinventory
                                    , RZ.`Locator`
                                    , RZ.State
                                    , RZ.Status
                                    , RZ.`Receipt Date`
                                    , tut.Nombre_short
                                    , fecha_hora, serie,
                                    id_tecnico_origen, id_tecnico_destino,
                                    observacion, id_movimiento, motivo, ticket , tut.id
                                    , tut.nombre as Super
                                    FROM
                                    ( SELECT Serial, Item, Org, Revision, Subinventory, `Locator`, Operation, Job
                                    , Step, Lot, State, Status, `Receipt Date`, `Ship Date`, `Supplier Name`
                                    , `Supplier Lot`, `Supplier Serial`, `Unit Number`, `Attributes`, `Unnamed: 20`
                                    FROM TB_LOGIST_bdReversa
                                    ) RZ
                                    LEFT JOIN
                                    tb_logis_movimientos A
                                    ON RZ.`Unit Number` = A.id
                                    LEFT JOIN TP_LOGIS_MOVIMIENTOS tlm ON A.id_movimiento = tlm.id
                                    LEFT JOIN tb_user_tqw tut ON tut.id = A.id_tecnico_origen
                                    LEFT JOIN tp_ferret_desc tfd ON RZ.Item = tfd.ID
                                    WHERE A.id_tecnico_destino = 164
                                    AND id_movimiento not in (12,4,7)
                                    AND State <> 'Issued out of stores'
                                        UNION ALL
                                        SELECT RZ.Serial
                                        , RZ.Item
                                        , RZ.Org
                                        , RZ.Subinventory
                                        , RZ.`Locator`
                                        , RZ.State
                                        , RZ.Status
                                        , RZ.`Receipt Date`
                                        , tut.Nombre_short
                                        , fecha_hora, serie,
                                        id_tecnico_origen, id_tecnico_destino,
                                        observacion, id_movimiento, motivo, ticket , tut.id
                                        , tut_super.nombre as Super

                                        FROM TB_FERRET_DIRECTA1 RZ
                                        LEFT JOIN
                                        (select * from
                                        (
                                        SELECT id, fecha_hora, serie,
                                        id_tecnico_origen, id_tecnico_destino,ticket,
                                        observacion, id_movimiento, motivo ,
                                        ROW_NUMBER() OVER (PARTITION BY serie ORDER BY fecha_hora DESC) AS row_num
                                        FROM TB_LOGIS_MOVIMIENTOS
                                        ) AS ranked
                                        WHERE row_num = 1
                                        ) A
                                        ON RZ.Serial = A.serie
                                        LEFT JOIN TP_LOGIS_MOVIMIENTOS tlm ON A.id_movimiento = tlm.id
                                        LEFT JOIN tb_user_tqw tut ON tut.rut = RZ.Subinventory
                                        LEFT JOIN tb_user_tqw tut_super ON tut_super.id = A.id_tecnico_origen
                                        WHERE A.id_movimiento in (2,3,4)
                                        AND A.id_tecnico_destino = 164
                                        AND motivo = 'FALLO ELECTRONICO'
                                        AND State <> 'Issued out of stores'";



                                            // Ejecutar la consulta
                                            $resultado_reversa = $conex->query($query_reversa);

                                            // Verificar si la consulta fue exitosa
                                            if ($resultado_reversa) {
                                            while ($fila = mysqli_fetch_assoc($resultado_reversa)) {
                                            echo '<tr>';
                                                echo '<td class="button-cell">' . $fila['Serial'] . '</td>';
                                                echo '<td class="button-cell">' . $fila['Nombre_short'] . '
                                                </td>';
                                                echo '<td class="button-cell">' . $fila['Org'] . '</td>';
                                                echo '<td class="button-cell">' . $fila['Subinventory'] . '
                                                </td>';
                                                echo '<td class="button-cell">' . $fila['motivo'] . '</td>';
                                                echo '<td class="button-cell">' . $fila['fecha_hora'] . '
                                                </td>';
                                                echo '<td class="button-cell"><button type="button" class="btn-warning"
                                                        onclick="redirigirEnTransferencia(\'' . $fila['Serial'] . '\', \'' . $fila['Item'] . '\', ' . $id_usuario . ', \'VER\')"><i
                                                            class="bi bi-eye-fill"></i></button></td>';
                                                echo '<td class="button-cell"><button type="button" class="btn-success"
                                                        onclick="rechazoMaterial(\'' . $fila['Serial'] . '\', \'' . $fila['id_tecnico_origen'] . '\', ' . $id_usuario . ', \'ACEPTA REVERSA\')"><i
                                                            class="bi bi-house-add-fill"></i></button></td>
                                                ';
                                                echo '<td class="button-cell"><button type="button" class="btn-danger"
                                                        onclick="rechazoMaterial(\'' . $fila['Serial'] . '\', \'' . $fila['id_tecnico_origen'] . '\', ' . $fila['id_tecnico_origen'] . ', \'BODEGA RECHAZA REVERSA\')"><i
                                                            class="bi bi-journal-x"></i></button></td>';
                                                echo '</tr>';
                                            }
                                            } else {
                                            // Manejar el error si la consulta falla
                                            echo '<tr>
                                                <td colspan="9">Error al cargar los datos: ' . $conex->error
                                                    . '</td>
                                            </tr>';
                                            }
                                    ?>

                                </tbody>
                            </table>
                        </div> <!-- Close scroll container -->
                    </div>

                </div>

                <!-- Cuarta tabla (ocupa el 100% del ancho) -->
                <div class="row mb-4" id="aceptada-container">
                    <div class="col-12">
                        <h4>Reversa Aceptada</h4>
                        <!-- Campo de búsqueda para la cuarta tabla -->
                        <div class="input-group mb-3">
                            <span class="input-group-text"><i class="bi bi-search"></i></span>
                            <input type="text" id="filtroReversaAceptada" class="form-control" placeholder="Buscar en la tabla...">
                        </div>
                        <div style="max-height: 400px; overflow-y: auto; overflow-x: auto; width: 100%;">
                            <table id="ReversaAceptada" class="table table-striped" style="width: 100%;">
                                <thead>
                                    <tr>
                                        <th>Serial</th>
                                        <th>Tecnico</th>
                                        <th>Fecha</th>
                                        <th>Historial</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $bd_reversa_acepta = $conex->query("
                                        SELECT
                                            Serial,
                                            tut.Nombre_short,
                                            A.fecha_hora,
                                            Item
                                        FROM (
                                            SELECT Serial, Item, Org, Revision, Subinventory, `Locator`,
                                            Operation, Job,
                                            Step, Lot, State, Status, `Receipt Date`, `Ship Date`, `Supplier Name`,
                                            `Supplier Lot`, `Supplier Serial`, `Unit Number`, `Attributes`,
                                            `[ ]`, `Unnamed: 20`
                                            FROM TB_LOGIST_bdReversa
                                        ) RZ
                                        LEFT JOIN tb_logis_movimientos A ON RZ.`Unit Number` = A.id
                                        LEFT JOIN TP_LOGIS_MOVIMIENTOS tlm ON A.id_movimiento = tlm.id
                                        LEFT JOIN tb_user_tqw tut ON SUBSTRING(tut.rut, 1, LENGTH(tut.rut) - 2) = SUBSTRING(`Locator`, 1, LENGTH(`Locator`) - 2)
                                        LEFT JOIN tb_user_tqw tut_destino ON tut_destino.id = A.id_tecnico_destino
                                        LEFT JOIN tp_ferret_desc tfd ON RZ.Item = tfd.ID
                                        WHERE tut_destino.id = " . $id_usuario . "
                                        AND State <> 'Issued out of stores'
                                        AND id_movimiento = 12");

                                    if ($bd_reversa_acepta && $bd_reversa_acepta->num_rows > 0) {
                                        while ($fila = mysqli_fetch_assoc($bd_reversa_acepta)) {
                                            echo '<tr>';
                                            echo '<td class="button-cell">' . $fila['Serial'] . '</td>';
                                            echo '<td class="button-cell">' . $fila['Nombre_short'] . '</td>';
                                            echo '<td class="button-cell">' . $fila['fecha_hora'] . '</td>';
                                            echo '<td class="button-cell"><button type="button" class="btn-warning" onclick="redirigirEnTransferencia(\'' . $fila['Serial'] . '\', \'' . $fila['Item'] . '\', ' . $id_usuario . ', \'VER\')"><i class="bi bi-eye-fill"></i></button></td>';
                                            echo '</tr>';
                                        }
                                    } else {
                                        echo '<tr><td colspan="4" class="text-center">
                                                <div class="alert alert-info">
                                                    <i class="bi bi-info-circle"></i>
                                                    No hay materiales de reversa para aceptar.
                                                </div>
                                              </td></tr>';
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        </div>


        <div id="crm-container" class="section-container" style="display: none;">


        </div>





        <div id="ecommerce-container" class="section-container" style="display: none; background-color: white;">

        </div>


        <div id="projects-container" class="section-container" style="display: none;">

        </div>

        <div id="courses-container" class="section-container" style="display: none;">

        </div>

        <div id="marketing-container" class="section-container" style="display: none;">

        </div>



    <div id="proactive-container" class="section-container" style="display: none;">


    </div>

    <div id="cubos-container" class="section-container" style="display: none;">

        <div class="col-md-12">
            <div class="card rounded-3 shadow mb-4">
                <div class="card-body">
                    <div class="d-flex align-items-center flex-row flex-wrap">

                        <div class="w-100 mb-3">
                            <h5 class="card-title">CUBO DE GESTIÓN LOGISTICO</h5>
                        </div>

                        <form id="searchForm" class="form-inline d-flex align-items-center gap-3">

                            <!-- Input for Serie -->
                            <div class="form-group mb-3">
                                <label for="serie" class="form-label">Serie:</label>
                                <input type="text" id="serie" name="serie" class="form-control"
                                    placeholder="Ingrese serie">
                            </div>

                            <!-- Combo Box for Técnico -->
                            <div class="form-group mb-3">
                                <label for="tecnico" class="form-label">Técnico:</label>
                                <select id="tecnico" name="tecnico" class="form-control">
                                    <option value="" selected disabled>Seleccione técnico</option>
                                    <?php
                                    if ($result_tecnicos && $result_tecnicos->num_rows > 0) {
                                        while($row = $result_tecnicos->fetch_assoc()) {
                                            echo '<option value="' . htmlspecialchars($row['Nombre_short']) . '">'
                                                . htmlspecialchars($row['Nombre_short']) . '</option>';
                                        }
                                    } else {
                                        echo '<option value="" disabled>No hay técnicos disponibles</option>';
                                    }
                                    ?>
                                </select>
                            </div>

                            <!-- Date Range Filter -->
                            <div class="form-group mb-3">
                                <label for="fecha_inicio" class="form-label">Fecha Inicio:</label>
                                <input type="date" id="fecha_inicio" name="fecha_inicio" class="form-control"
                                    value="<?php echo $thirtyDaysAgo; ?>" max="<?php echo $today; ?>">
                            </div>

                            <div class="form-group mb-3">
                                <label for="fecha_final" class="form-label">Fecha Final:</label>
                                <input type="date" id="fecha_final" name="fecha_final" class="form-control"
                                    value="<?php echo $today; ?>" max="<?php echo $today; ?>">
                            </div>

                            <div class="form-group mb-3">
                                <label class="form-label  d-inline-block" style="margin-top: 7px;"></label>
                                <!-- Label invisible para mantener alineación -->
                                <button id="customSearchButton" class="btn btn-success form-control">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>

                            <div class="form-group mb-3">
                                <label class="form-label  d-inline-block" style="margin-top: 7px;"></label>
                                <!-- Label invisible para mantener alineación -->
                                <button id="downloadTable" class="btn btn-success form-control">
                                    <i class="fas fa-download"></i>
                                </button>
                            </div>

                            <div class="form-group mb-3">
                                <label class="form-label  d-inline-block" style="margin-top: 7px;"></label>
                                <!-- Label invisible para mantener alineación -->
                                <button id="actualizarTablaMovimientos" class="btn btn-success form-control">
                                    <i class="bi bi-arrow-clockwise"></i>
                                </button>
                            </div>

                        </form>
                        <form>
                            <p class="text-muted fst-italic">Estas viendo los ultimos 100 registros de gestión en la APP Logística</p>
                        </form>
                        <div id="resultTable"
                            style="max-height: 450px; overflow: auto;margin: 20px 10px 15px 10px;display: block;">



                            <?php
                                // Asumiendo que ya tienes una conexión a la base de datos establecida
                                // Reemplaza $conn con tu variable de conexión a la base de datos
                                $sql = "SELECT  fecha_hora, A.serie,
                                    b.`Nombre_short` as Tecnico1 , c.`Nombre_short` as Tecnico2
                                    , observacion, `Semantica`, motivo, ticket, archivo_adj
                                    , ins.rut
                                    , ins.orden
                                    , A.id
                                    , A.flag_bodega_final
                                    FROM tb_logis_movimientos A
                                    LEFT JOIN tb_user_tqw b on b.id = id_tecnico_origen
                                    LEFT JOIN tb_user_tqw c on c.id = id_tecnico_destino
                                    LEFT JOIN tp_logis_movimientos d on d.id = id_movimiento
                                    LEFT JOIN TB_LOGIS_RUT_ORDEN_FORM ins ON ins.id_mov = A.id
                                    ORDER BY fecha_hora DESC
                                    LIMIT 200";
                                $result = $conex->query($sql);

                                if ($result && $result->num_rows > 0) {
                                    echo '<table class="tabla-styled" id="tabla_detalle_series">';
                                    echo '<thead>';
                                    echo '<tr>';
                                    echo '<th>Id</th>';
                                    echo '<th>Fecha Hora</th>';
                                    echo '<th>Serie</th>';
                                    echo '<th>Técnico Origen</th>';
                                    echo '<th>Técnico Destino</th>';
                                    echo '<th>Observación</th>';
                                    echo '<th>Semántica</th>';
                                    echo '<th>Motivo</th>';
                                    echo '<th>Archivo Adjunto</th>';
                                    echo '<th>Rut declarado</th>';
                                    echo '<th>Orden declarado</th>';
                                    echo '<th>Flag bodega fin</th>';
                                    echo '</tr>';
                                    echo '</thead>';
                                    echo '<tbody>';

                                    // Fetch and display each row of data
                                    while ($row = $result->fetch_assoc()) {
                                        echo '<tr>';
                                        echo '<td>' . htmlspecialchars($row['id']) . '</td>';
                                        echo '<td>' . htmlspecialchars($row['fecha_hora']) . '</td>';
                                        echo '<td>' . htmlspecialchars($row['serie']) . '</td>';
                                        echo '<td>' . htmlspecialchars($row['Tecnico1']) . '</td>';
                                        echo '<td>' . htmlspecialchars($row['Tecnico2']) . '</td>';
                                        echo '<td>' . htmlspecialchars(substr($row['observacion'], 0, 40)) . '</td>';
                                        echo '<td>' . htmlspecialchars($row['Semantica']) . '</td>';
                                        echo '<td>' . htmlspecialchars(substr($row['motivo'], 0, 40)) . '</td>';
                                        echo '<td>' . ($row['archivo_adj'] ? '<a href="' . htmlspecialchars($row['archivo_adj']) . '" target="_blank">Ver registro</a>' : '') . '</td>';
                                        echo '<td>' . htmlspecialchars($row['rut']) . '</td>';
                                        echo '<td>' . htmlspecialchars($row['orden']) . '</td>';
                                        echo '<td>' . htmlspecialchars($row['flag_bodega_final']) . '</td>';
                                        echo '</tr>';
                                    }

                                    echo '</tbody>';
                                    echo '</table>';
                                } else {
                                    echo 'No se encontraron resultados.';
                                }
                                ?>
                        </div>

                    </div>
                </div>
            </div>
        </div>

    </div>

    <div id="facturacion-container" class="section-container" style="display: none;">

    </div>

    <div id="inventario-container" class="section-container" style="display: none;">

    </div>
    </div>


    <!----------- POP UP COMBO - RECHAZO TECNICO --------->
    <div id="popup-container" class="modal-overlay" style="display: none;">
        <div id="popup" class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Rechazo de material</h5>
                <button type="button" class="btn-close" aria-label="Close" onclick="Rechazocancelar()"></button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="popupSerial">
                <input type="hidden" id="popupTicket">
                <input type="hidden" id="popupIdTecnicoDestino">
                <input type="hidden" id="popupAccion">

                <div class="form-group mb-4">
                    <label for="motivoRechazo" class="form-label">Motivo:</label>
                    <select id="motivoRechazo" class="form-select">
                        <option value="" selected disabled>Seleccione un motivo</option>
                        <option value="Rechazo justificación">Rechazo justificación</option>
                        <option value="Se entrega serie">Se entrega serie</option>
                        <option value="Se regulariza serie">Se regulariza serie</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label" for="obs_insta">Observaciones</label>
                    <textarea class="form-control" id="obs_insta" placeholder="Rechazado por ... " name="obs_insta" cols="3"
                        rows="5" required></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-confirm" onclick="Rechazoaceptar()">Aceptar</button>
                <button class="btn-cancel" onclick="Rechazocancelar()">Cancelar</button>
            </div>
        </div>
    </div>

    <div class="offcanvas offcanvas-end" id="offcanvasHistorial" data-bs-scroll="true" tabindex="-1"
            aria-labelledby="affanOffcanvsLabel">

            <button class="btn-close btn-close-white text-reset" type="button" data-bs-dismiss="offcanvas"
                aria-label="Close"></button>

            <!-- Offcanvas Body with Form -->
            <div class="offcanvas-body p-4">
                <h5>Historial de movimiento</h5>


                <div class="mb-3">
                    <label for="icon_bootstrap" class="form-label">SERIE</label>
                    <input type="text" class="form-control" placeholder="" id="serieHistorial" name="serieHistorial"
                        readonly>
                </div>

                <div class="mb-3">
                    <label for="icon_bootstrap" class="form-label">FAMILIA</label>
                    <input type="text" class="form-control" placeholder="" id="serieHistorial" name="serieHistorial"
                        readonly>
                </div>

                <div class="timeline-container" id="webHistorial">

                </div>

            </div>
        </div>

    <!-- Modal para mostrar las solicitudes -->
    <div class="modal-overlay" id="solicitudesModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Historial Solicitudes de Técnicos</h5>
                <button type="button" class="btn-close" aria-label="Close" onclick="cerrarModalSolicitudes()"></button>
            </div>
            <div class="modal-body">
                <div id="solicitudesContenido">
                    <!-- Aquí se cargará la tabla con las solicitudes -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-cancel" onclick="cerrarModalSolicitudes()">Cerrar</button>
            </div>
        </div>
    </div>

    <!-- Core Libraries -->
    <!-- jQuery already included in head -->
    
    <!-- Script para abrir el modal de solicitudes -->
    <script>
        function abrirModalSolicitudes() {
            console.log('Función abrirModalSolicitudes ejecutada');
            const modalElement = document.getElementById('solicitudesModal');
            if (modalElement) {
                console.log('Modal encontrado, mostrando');
                modalElement.style.display = 'flex';
                modalElement.classList.add('show');
                document.body.style.overflow = 'hidden';
                cargarSolicitudes();
            } else {
                console.error('Modal no encontrado');
                alert('Error: No se encontró el modal de solicitudes');
            }
        }
    </script>

    <script src="https://unpkg.com/xlsx/dist/xlsx.full.min.js"></script>


    <!-- Bootstrap Core -->
    <!-- Bootstrap CSS already included in head -->
    <!-- Bootstrap JS already included in head -->
    <!-- Note: bootstrap.bundle.min.js already includes Popper.js -->

    <!-- Charting Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-3d@1.0.0/dist/chartjs-plugin-3d.min.js">
    </script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>

    <!-- Application Specific Scripts -->
    <script>
        // Declarar variable global con el ID del usuario
        const PHP_USER_ID = <?php echo isset($id_usuario) ? $id_usuario : 0; ?>;
    </script>

    <!-- Agregar input hidden para el userId -->
    <input type="hidden" id="userId" value="<?php echo isset($id_usuario) ? $id_usuario : 0; ?>">

    <script src="js/animations.js"></script>
    <script src="js/main.js"></script>
    <script src="js/sidebar.js"></script>
    <script src="js/home_bodega_new.js"></script>

    <!-- Asegúrate de que estos scripts estén incluidos en el orden correcto -->
    <!-- Popper.js and Bootstrap.js already included via bootstrap.bundle.min.js in head -->

    <!-- Modal de Solicitudes -->
    <div id="solicitudesModal" class="modal-overlay" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.7); z-index: 1000; align-items: center; justify-content: center;">
        <div class="modal-content" style="background: white; padding: 20px; border-radius: 5px; max-width: 90%; max-height: 90%; overflow: auto;">
            <div class="modal-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; border-bottom: 1px solid #ddd; padding-bottom: 10px;">
                <h5 class="modal-title">Solicitudes</h5>
                <button type="button" class="btn-close" aria-label="Cerrar" onclick="document.getElementById('solicitudesModal').style.display='none'"></button>
            </div>
            <div class="modal-body" id="solicitudesContenido">
                Cargando solicitudes...
            </div>
        </div>
    </div>

    
    <script>
        // Función explícita para mostrar el modal de solicitudes
        function mostrarModalSolicitudes(e) {
            console.log('Función mostrarModalSolicitudes ejecutada');
            if (e) {
                e.preventDefault();
                e.stopPropagation();
            }
            
            const modalElement = document.getElementById('solicitudesModal');
            if (modalElement) {
                console.log('Modal encontrado, mostrando modal');
                modalElement.style.display = 'flex';
                modalElement.classList.add('show');
                document.body.style.overflow = 'hidden';
                
                // Cargar el contenido del modal
                cargarSolicitudes();
                
                // Reinicializar select2 después de mostrar el modal
                setTimeout(function() {
                    if (typeof $.fn.select2 !== 'undefined') {
                        console.log('Inicializando Select2 después de abrir el modal');
                        try {
                            $('.select2-search').select2({
                                placeholder: "Seleccione o escriba para buscar",
                                allowClear: true,
                                width: '100%',
                                dropdownParent: $('#solicitudesModal'),
                                language: {
                                    noResults: function() {
                                        return "No se encontraron resultados";
                                    },
                                    searching: function() {
                                        return "Buscando...";
                                    }
                                }
                            }).on('select2:open', function() {
                                // Forzar foco en el campo de búsqueda
                                setTimeout(function() {
                                    $('.select2-search__field').focus();
                                }, 100);
                            });
                        } catch (e) {
                            console.error('Error inicializando Select2:', e);
                        }
                    } else {
                        console.warn('Select2 no está disponible');
                    }
                }, 500);
            } else {
                console.error('Elemento modal no encontrado');
                alert('Error: No se pudo encontrar el modal de solicitudes');
            }
        }

        // Inicialización general de eventos al cargar la página
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM cargado - Configurando eventos para solicitudes');
            
            // Asegurar que el evento click del botón funcione correctamente
            const btnSolicitudes = document.getElementById('btnHistorial');
            if (btnSolicitudes) {
                console.log('Botón de solicitudes encontrado, asignando eventos');
                // Asignar evento onclick como respaldo adicional
                btnSolicitudes.onclick = abrirModalSolicitudes;
            } else {
                console.warn('Botón #btnHistorial no encontrado en DOMContentLoaded');
            }
            
            // Manejador delegado como última opción de respaldo
            $(document).on('click', '#btnHistorial', function(e) {
                console.log('Evento delegado jQuery activado');
                abrirModalSolicitudes(e);
            });
        });

        // Función para cerrar el modal de solicitudes
        function cerrarModalSolicitudes() {
            console.log('Cerrando modal de solicitudes');
            const modalElement = document.getElementById('solicitudesModal');
            if (modalElement) {
                modalElement.classList.remove('show');
                modalElement.style.display = 'none';
                document.body.style.overflow = ''; // Restaurar scroll
                console.log('Modal cerrado correctamente');
            } else {
                console.error('No se encontró el elemento modal para cerrar');
            }
        }

        // Agregar evento para cerrar el modal con la tecla Escape
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                cerrarModalSolicitudes();
            }
        });

        // Agregar evento para cerrar el modal al hacer clic fuera del contenido
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Configurando eventos para cerrar el modal');
            const modalOverlay = document.getElementById('solicitudesModal');
            if (modalOverlay) {
                console.log('Modal overlay encontrado, configurando evento click');
                modalOverlay.addEventListener('click', function(event) {
                    // Si el clic fue directamente en el overlay (no en su contenido)
                    if (event.target === modalOverlay) {
                        console.log('Clic detectado fuera del contenido del modal');
                        cerrarModalSolicitudes();
                    }
                });
                
                // Configurar evento click para el botón de cerrar
                const closeButton = modalOverlay.querySelector('.btn-close');
                if (closeButton) {
                    console.log('Botón de cerrar encontrado, configurando evento');
                    closeButton.addEventListener('click', function(event) {
                        console.log('Botón cerrar clickeado');
                        event.preventDefault();
                        event.stopPropagation();
                        cerrarModalSolicitudes();
                    });
                } else {
                    console.error('Botón de cerrar no encontrado');
                }
                
                // Configurar evento click para el botón de cerrar del footer
                const footerCloseButton = modalOverlay.querySelector('.modal-footer .btn-cancel');
                if (footerCloseButton) {
                    console.log('Botón de cerrar en footer encontrado, configurando evento');
                    footerCloseButton.addEventListener('click', function(event) {
                        console.log('Botón cerrar del footer clickeado');
                        event.preventDefault();
                        event.stopPropagation();
                        cerrarModalSolicitudes();
                    });
                }
            } else {
                console.error('Modal overlay no encontrado');
            }
            
            // Agregar manejadores con jQuery por si los anteriores fallan
            $(document).on('click', '#solicitudesModal .btn-close, #solicitudesModal .btn-cancel', function(e) {
                console.log('Botón de cerrar clickeado (jQuery)');
                e.preventDefault();
                e.stopPropagation();
                cerrarModalSolicitudes();
            });
            
            $(document).on('click', '#solicitudesModal', function(e) {
                if (e.target === this) {
                    console.log('Clic en overlay detectado (jQuery)');
                    cerrarModalSolicitudes();
                }
            });
            
            // Verificación adicional de elementos clave
            console.log('=== VERIFICACIÓN DE ELEMENTOS EN EL DOM ===');
            console.log('Botón mostrarSolicitudes:', document.getElementById('btnHistorial'));
            console.log('Modal solicitudes:', document.getElementById('solicitudesModal'));
            console.log('==============================');
        });

        // Función para mostrar mensajes de error
        function mostrarError(error) {
            $('#solicitudesContenido').html(`<div class="alert alert-danger">Error al cargar las solicitudes: ${error}</div>`);
        }

        // Función para inicializar filtros de tabla
        function initializeTableFilters() {
            // Obtener todas las tablas en el contenido cargado
            const tables = $('#solicitudesContenido table');

            // Para cada tabla, inicializar la funcionalidad de filtrado
            tables.each(function() {
                const tableId = $(this).attr('id');
                if (tableId) {
                    // Buscar el input de búsqueda correspondiente
                    const searchInput = $(`#searchInput${tableId}`);

                    if (searchInput.length) {
                        // Agregar evento de entrada para filtrar la tabla
                        searchInput.on('input', function() {
                            const searchText = $(this).val().toLowerCase();
                            $(`#${tableId} tbody tr`).filter(function() {
                                $(this).toggle($(this).text().toLowerCase().indexOf(searchText) > -1);
                            });
                        });
                    }
                }
            });
        }

        // Función para cargar las solicitudes
        function cargarSolicitudes() {
            console.log('Ejecutando función cargarSolicitudes()');
            
            // Verificar que el contenedor existe
            const contenedor = document.getElementById('solicitudesContenido');
            if (!contenedor) {
                console.error('ELEMENTO NO ENCONTRADO: #solicitudesContenido');
                alert('Error: No se encontró el contenedor para mostrar las solicitudes');
                return;
            }
            
            // Mostrar indicador de carga
            contenedor.innerHTML = `
                <div class="col-md-12">
                    <div class="card rounded-3 shadow mb-4">
                        <div class="card-body">
                            <div class="d-flex align-items-center flex-row flex-wrap">
                                <div class="w-100">
                                    <h5 class="card-title">Solicitudes de materiales</h5>
                                </div>
                                <div id="resultTable2" style="max-height: 450px; overflow: auto; margin: 20px 10px 15px 10px; width: 100%;">
                                    <div class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Cargando...</span>
                                        </div>
                                        <p class="mt-2">Cargando solicitudes...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            console.log('Enviando solicitud AJAX a get_solicitudes_filtradas.php con sesión: <?php echo $sesion; ?>');
            
            // Usar try-catch para capturar errores
            try {
                $.ajax({
                    url: 'get_solicitudes_filtradas.php?id_sesion=<?php echo $sesion; ?>',
                    method: 'GET',
                    success: function(data) {
                        console.log('Datos recibidos exitosamente, longitud:', data.length);
                        // Insertar los datos directamente
                        contenedor.innerHTML = data;
                        
                        // Inicializar Select2 en el select después de cargar el contenido
                        setTimeout(function() {
                            try {
                                if (typeof $.fn.select2 !== 'undefined') {
                                    $('.select2-search').select2({
                                        placeholder: "Seleccione o escriba para buscar",
                                        allowClear: true,
                                        width: '100%',
                                        dropdownParent: $('#solicitudesModal'),
                                        language: {
                                            noResults: function() {
                                                return "No se encontraron resultados";
                                            },
                                            searching: function() {
                                                return "Buscando...";
                                            }
                                        }
                                    });
                                    console.log('Select2 inicializado como alternativa');
                                } else {
                                    console.error('Ninguna biblioteca de select con búsqueda está disponible');
                                }
                            } catch (e) {
                                console.error('Error al inicializar select con búsqueda:', e);
                            }
                        }, 200);
    
                        // Adjuntar manejadores de eventos para los filtros del formulario
                        setTimeout(function() {
                            // Configurar el botón de búsqueda
                            $('#buscarSolicitudBtn').on('click', function() {
                                aplicarFiltrosSolicitudes();
                            });
                            
                            // Configurar el evento de tecla Enter en los campos de filtro
                            const inputsFiltro = $('#searchForm2 input, #searchForm2 select');
                            inputsFiltro.each(function() {
                                $(this).on('keypress', function(e) {
                                    if (e.key === 'Enter') {
                                        e.preventDefault();
                                        aplicarFiltrosSolicitudes();
                                    }
                                });
                            });
                        }, 500);
                    },
                    error: function(xhr, status, error) {
                        console.error('Error al cargar las solicitudes:', error);
                        console.error('Estado de la solicitud:', status);
                        console.error('Respuesta del servidor:', xhr.responseText);
                        contenedor.innerHTML = `
                            <div class="alert alert-danger"> 
                                <strong>Error:</strong> No se pudieron cargar las solicitudes.<br>
                                Detalles: ${error}<br>
                                <button class="btn btn-sm btn-outline-danger mt-2" onclick="cargarSolicitudes()">Reintentar</button>
                            </div>
                        `;
                    }
                });
            } catch (e) {
                console.error('Excepción en función cargarSolicitudes:', e);
                contenedor.innerHTML = `
                    <div class="alert alert-danger">
                        <strong>Error:</strong> Se produjo una excepción.<br> 
                        Detalles: ${e.message}
                        <button class="btn btn-sm btn-outline-danger mt-2" onclick="cargarSolicitudes()">Reintentar</button>
                    </div>
                `;
            }
        }
        
        // Función para aplicar los filtros a la tabla de solicitudes mediante solicitud AJAX
        function aplicarFiltrosSolicitudes() {
            const tecnico = $('#tecnico_solic').val() || '';
            const fechaInicio = $('#fecha_inicio_solic').val() || '';
            const fechaFin = $('#fecha_final_solic').val() || '';
            const tipoSolicitud = $('#tipo_solicitud').val() || '';
            
            console.log('Aplicando filtros al servidor:', {
                tecnico: tecnico,
                fechaInicio: fechaInicio,
                fechaFin: fechaFin,
                tipoSolicitud: tipoSolicitud
            });
            
            // Obtener solo la tabla dentro del contenedor
            const tablaContainer = $('#resultTable2');
            if (tablaContainer.length) {
                // Solo actualizar la tabla, no todo el contenido
                tablaContainer.html('<div class="text-center mt-4"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Cargando...</span></div><p class="mt-2">Buscando solicitudes con los filtros seleccionados...</p></div>');
            } else {
                // Si no se encuentra el contenedor de la tabla, actualizar todo el contenido
                $('#solicitudesContenido').html('<div class="text-center mt-4"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Cargando...</span></div><p class="mt-2">Buscando solicitudes con los filtros seleccionados...</p></div>');
            }
            
            // Construir URL con parámetros de filtro
            let url = 'get_solicitudes_filtradas.php?id_sesion=<?php echo $sesion; ?>';
            
            // Añadir parámetros de filtro si están definidos
            if (tecnico) url += `&tecnico=${encodeURIComponent(tecnico)}`;
            if (fechaInicio) url += `&fecha_inicio=${encodeURIComponent(fechaInicio)}`;
            if (fechaFin) url += `&fecha_fin=${encodeURIComponent(fechaFin)}`;
            if (tipoSolicitud) url += `&tipo_solicitud=${encodeURIComponent(tipoSolicitud)}`;
            
            // Realizar solicitud AJAX
            $.ajax({
                url: url,
                method: 'GET',
                success: function(data) {
                    // Insertar los datos filtrados en el contenedor
                    $('#solicitudesContenido').html(data);
                    
                    // Volver a inicializar Select2
                    setTimeout(function() {
                        try {
                            console.log('Reininicializando Select2 después de filtrar...');
                            // Destruir instancias previas para evitar duplicados
                            $('.select2-search').select2('destroy');
                            
                            // Crear nueva instancia
                            $('.select2-search').select2({
                                placeholder: "Seleccione o escriba para buscar",
                                allowClear: true,
                                width: '100%',
                                dropdownParent: $('#solicitudesModal'),
                                language: {
                                    noResults: function() {
                                        return "No se encontraron resultados";
                                    },
                                    searching: function() {
                                        return "Buscando...";
                                    }
                                }
                            }).on('select2:open', function() {
                                // Forzar foco en el campo de búsqueda
                                setTimeout(function() {
                                    $('.select2-search__field').focus();
                                }, 100);
                            });
                            console.log('Select2 reinicializado correctamente');
                        } catch(e) {
                            console.error('Error al reinicializar Select2:', e);
                        }
                    }, 300);
                    
                    // Verificar si hay resultados
                    const filasVisibles = $('#tablaSolicitudes tbody tr').length;
                    console.log(`Filas recibidas del servidor: ${filasVisibles}`);
                    
                    // Reinicializar eventos después de cargar el nuevo contenido
                    setTimeout(function() {
                        // Reconfigurar eventos del formulario
                        $('#buscarSolicitudBtn').on('click', aplicarFiltrosSolicitudes);
                        
                        // Configurar evento de tecla Enter en los campos de filtro
                        $('#searchForm2 input, #searchForm2 select').each(function() {
                            $(this).on('keypress', function(e) {
                                if (e.key === 'Enter') {
                                    e.preventDefault();
                                    aplicarFiltrosSolicitudes();
                                }
                            });
                        });
                    }, 300);
                },
                error: function(xhr, status, error) {
                    // Mostrar mensaje de error
                    $('#solicitudesContenido').html(
                        `<div class="alert alert-danger mt-3">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            Error al cargar los datos: ${error}
                            <hr>
                            <button type="button" class="btn btn-outline-danger" onclick="cargarSolicitudes()">
                                <i class="bi bi-arrow-repeat me-1"></i> Reintentar
                            </button>
                        </div>`
                    );
                    console.error('Error en la solicitud AJAX:', error);
                }
            });
        }

        // Función para exportar la tabla a Excel (XLSX)
        function exportTableToExcel(tableID, filename = '') {
            // Seleccionar la tabla
            const table = document.getElementById(tableID);
            if (!table) {
                console.error('Tabla no encontrada');
                return;
            }

            // Crear una tabla HTML para Excel
            let html = '<table border="1">';
            const rows = table.querySelectorAll('tr');

            // Agregar filas a la tabla HTML
            for (let i = 0; i < rows.length; i++) {
                html += '<tr>';
                const cols = rows[i].querySelectorAll('td, th');

                for (let j = 0; j < cols.length; j++) {
                    // Determinar si es un encabezado
                    const isHeader = cols[j].tagName === 'TH';

                    // Obtener solo el texto, eliminando cualquier HTML
                    const cellText = cols[j].innerText.trim();

                    // Agregar la celda con formato adecuado
                    if (isHeader) {
                        html += '<th style="background-color: #f8f9fa; font-weight: bold;">' + cellText + '</th>';
                    } else {
                        // Si es la columna de estado, preservar el formato
                        if (j === 5) { // Índice de la columna de estado
                            const estado = cellText.toLowerCase();
                            if (estado === 'pendiente') {
                                html += '<td style="background-color: #fff3cd; color: #856404;">' + cellText + '</td>';
                            } else if (estado === 'realizado') {
                                html += '<td style="background-color: #d4edda; color: #155724;">' + cellText + '</td>';
                            } else {
                                html += '<td>' + cellText + '</td>';
                            }
                        } else {
                            html += '<td>' + cellText + '</td>';
                        }
                    }
                }
                html += '</tr>';
            }

            html += '</table>';

            // Configurar para descarga
            const blob = new Blob([html], { type: 'application/vnd.ms-excel' });
            const link = document.createElement('a');

            // Crear un URL para el blob
            const url = URL.createObjectURL(blob);

            // Configurar el enlace de descarga
            link.setAttribute('href', url);
            link.setAttribute('download', filename + '.xls');
            
            // Agregar al DOM
            document.body.appendChild(link);

            // Simular clic
            link.click();

            // Limpiar
            document.body.removeChild(link);
        }
    </script>

    <!-- Include Stagewise at the end of body -->
    <script src="js/stagewise-init.js"></script>
    
    <!-- Script para verificar semáforo y solucionar problemas -->
    <script>
    // Verificar el estado del semáforo después de cargar la página
    window.addEventListener('load', function() {
        console.log('Página completamente cargada: ' + new Date().toLocaleTimeString());
        
        // Verificar si hubo error en el semáforo
        const errorSemaforo = document.querySelector('.alert-warning');
        if (errorSemaforo && errorSemaforo.textContent.includes('Error al cargar los datos del semáforo')) {
            console.error('Se detectó un error en la carga del semáforo');
            
            // Verificar recursos cargados
            const scripts = document.querySelectorAll('script');
            let foundLazyLoading = false;
            
            scripts.forEach(script => {
                if (script.src && script.src.includes('lazy-loading.js')) {
                    foundLazyLoading = true;
                    console.log('Script lazy-loading.js está cargado correctamente');
                }
            });
            
            if (!foundLazyLoading) {
                console.error('No se encontró el script lazy-loading.js. Esto podría ser la causa del error.');
            }
            
            // Intentar recargar la página automáticamente si es la primera vez que ocurre el error
            if (!sessionStorage.getItem('reloaded_semaforo')) {
                sessionStorage.setItem('reloaded_semaforo', 'true');
                console.log('Intentando recargar la página para resolver el error...');
                // Recargar después de 3 segundos
                setTimeout(function() {
                    location.reload();
                }, 3000);
            }
        }
    });
    </script>
</body>
</html>
