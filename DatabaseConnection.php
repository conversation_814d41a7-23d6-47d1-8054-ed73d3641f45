<?php
/**
 * Secure Database Connection Manager
 *
 * SECURITY FEATURES:
 * - Environment-based configuration
 * - Secure error handling with no credential exposure
 * - Comprehensive logging for debugging
 * - Connection pooling and resource management
 * - Rate limiting and security monitoring
 *
 * @version 2.0.0 - Security Enhanced
 */

// Define access constant for security
define('APP_ACCESS', true);

// Include secure configuration
require_once __DIR__ . '/config/database.php';

/**
 * Security logging functions - Must be defined before class
 */
function logSecurityEventGlobal($event, $context = []) {
    logSecureError("Security Event: $event", $context, 'SECURITY');
}

class DatabaseConnection {
    private static $instance = null;
    private $connection;
    private $activeConnections = 0;
    private $maxConnections = 150;
    private $lastActivity;
    private $connectionLogId;
    private $config;
    private $securityConfig;
    private $connectionAttempts = 0;
    private $maxConnectionAttempts = 3;

    private function __construct() {
        // Load secure configuration
        $this->config = getDatabaseConfig();
        $this->securityConfig = getSecurityConfig();

        $this->connect();
        $this->lastActivity = time();
    }

    private function connect() {
        $this->connectionAttempts++;

        try {
            // Rate limiting for connection attempts
            if ($this->connectionAttempts > $this->maxConnectionAttempts) {
                logSecurityEventGlobal('connection_rate_limit_exceeded', [
                    'attempts' => $this->connectionAttempts,
                    'max_attempts' => $this->maxConnectionAttempts
                ]);
                throw new DatabaseSecurityException("Too many connection attempts");
            }

            // Establish secure connection
            $this->connection = new mysqli(
                $this->config['host'],
                $this->config['user'],
                $this->config['password'],
                $this->config['database'],
                $this->config['port']
            );

            // Check for connection errors
            if ($this->connection->connect_error) {
                $this->handleConnectionError($this->connection->connect_error, $this->connection->connect_errno);
            }

            // Configure connection security
            $this->configureConnection();

            $this->activeConnections++;
            $this->logConnection();

            // Reset connection attempts on success
            $this->connectionAttempts = 0;

        } catch (mysqli_sql_exception $e) {
            $this->handleConnectionError($e->getMessage(), $e->getCode());
        } catch (Exception $e) {
            $this->handleGenericError($e);
        }
    }

    /**
     * Configure connection security settings
     */
    private function configureConnection() {
        // Set charset securely
        if (!$this->connection->set_charset($this->config['charset'])) {
            logSecurityEventGlobal('charset_configuration_failed', [
                'charset' => $this->config['charset'],
                'error' => $this->connection->error
            ]);
        }

        // Configure SSL if enabled
        if ($this->config['ssl'] && $this->securityConfig['environment'] === 'production') {
            // SSL configuration would go here
            // $this->connection->ssl_set(...);
        }

        // Set SQL mode for security
        $this->connection->query("SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'");
    }

    /**
     * Handle connection errors securely
     */
    private function handleConnectionError($errorMessage, $errorCode) {
        // Log detailed error for administrators (server-side only)
        logSecureError('Database connection failed', [
            'error_code' => $errorCode,
            'error_message' => $errorMessage,
            'host' => $this->config['host'],
            'database' => $this->config['database'],
            'port' => $this->config['port'],
            'attempt' => $this->connectionAttempts
        ], 'CRITICAL');

        // Throw user-safe exception
        throw new DatabaseConnectionException(getUserSafeErrorMessage('database'));
    }

    /**
     * Handle generic errors securely
     */
    private function handleGenericError($exception) {
        // Log detailed error for administrators
        logSecureError('Database connection exception', [
            'exception_class' => get_class($exception),
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString()
        ], 'ERROR');

        // Determine appropriate user message
        if ($exception instanceof DatabaseSecurityException) {
            throw new DatabaseConnectionException(getUserSafeErrorMessage('rate_limit'));
        } else {
            throw new DatabaseConnectionException(getUserSafeErrorMessage('database'));
        }
    }



    private function logConnection() {
        $usuario = isset($_SESSION['usuario']) ? $_SESSION['usuario'] : 'anonymous';
        $pagina = $_SERVER['PHP_SELF'];
        $ip = $_SERVER['REMOTE_ADDR'];
        
        // Reemplazar shell_exec con información segura del servidor
        $tcp_info = [
            'remote_addr' => $_SERVER['REMOTE_ADDR'],
            'server_port' => $_SERVER['SERVER_PORT'],
            'timestamp' => time()
        ];
        
        $query = "INSERT INTO tb_conexiones_log 
                 (usuario, pagina, estado, fecha_conexion, ip, tcp_state, tcp_info) 
                 VALUES (?, ?, 'ACTIVE', NOW(), ?, 1, ?)";
        
        $stmt = $this->connection->prepare($query);
        $json_tcp = json_encode($tcp_info);
        $stmt->bind_param("ssss", $usuario, $pagina, $ip, $json_tcp);
        $stmt->execute();
        $this->connectionLogId = $this->connection->insert_id;
    }

    public function closeConnection() {
        try {
            if ($this->connection instanceof mysqli) {
                // Verificar primero si la conexión sigue activa antes de hacer ping
                $isConnected = false;
                
                try {
                    // Usar un bloque try-catch interno para capturar excepciones del ping
                    $isConnected = $this->connection->ping();
                } catch (Exception $e) {
                    error_log("Error en ping de conexión: " . $e->getMessage());
                    $isConnected = false;
                }
                
                if ($isConnected && $this->connectionLogId) {
                    try {
                        $stmt = $this->connection->prepare(
                            "UPDATE tb_conexiones_log 
                             SET estado = 'CLOSED', 
                                 fecha_desconexion = NOW(),
                                 duracion = TIMESTAMPDIFF(SECOND, fecha_conexion, NOW()),
                                 tcp_state = 0
                             WHERE id = ? AND estado = 'ACTIVE'"
                        );
                        $stmt->bind_param("i", $this->connectionLogId);
                        $stmt->execute();
                        $stmt->close();
                    } catch (Exception $e) {
                        error_log("Error al actualizar log de conexión: " . $e->getMessage());
                    }
                }
                
                // Cerrar la conexión sólo si sigue activa
                if ($isConnected) {
                    $this->connection->close();
                    error_log("Conexión mysqli cerrada - ID: {$this->connectionLogId}");
                }
                
                $this->connection = null;
                $this->activeConnections = max(0, $this->activeConnections - 1);
                $this->connectionLogId = null;
            }
        } catch (Exception $e) {
            error_log("[" . date('Y-m-d H:i:s') . "] Error en closeConnection: " . $e->getMessage());
        }
    }

    // [Resto de métodos se mantienen igual...]
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function getConnection() {
        if (!$this->isConnectionAlive()) {
            $this->reconnect();
        }
        return $this->connection;
    }

    private function isConnectionAlive() {
        return $this->connection && $this->connection->ping();
    }

    private function reconnect() {
        $this->closeConnection();
        $this->connect();
    }

    public function cleanup() {
        $this->closeConnection();
    }

    public function __destruct() {
        $this->closeConnection();
    }

    private function __clone() {}
    
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }


    public function getConnectionStats() {
        if (!$this->connection) {
            return null;
        }
    
        try {
            $stats = [
                'active_connections' => $this->activeConnections,
                'max_connections' => $this->maxConnections,
                'connection_status' => $this->isConnectionAlive() ? 'alive' : 'dead'
            ];
            
            $result = $this->connection->query("SHOW STATUS WHERE Variable_name = 'Threads_connected'");
            if ($result && $row = $result->fetch_assoc()) {
                $stats['mysql_threads'] = $row['Value'];
                $result->free();
            }
            
            return $stats;
        } catch (Exception $e) {
            error_log("Error getting connection stats: " . $e->getMessage());
            return null;
        }
    }

    public static function checkOpenConnections() {
        $instance = self::getInstance();
        $conn = $instance->getConnection();
        
        $query = "SELECT 
                    pagina,
                    COUNT(*) as conexiones_activas,
                    MIN(fecha_conexion) as primera_conexion,
                    MAX(fecha_conexion) as ultima_conexion,
                    SUM(tcp_state) as conexiones_tcp
                 FROM tb_conexiones_log 
                 WHERE estado = 'ACTIVE'
                 GROUP BY pagina
                 ORDER BY conexiones_activas DESC";
                 
        $result = $conn->query($query);
        
        $report = [];
        while ($row = $result->fetch_assoc()) {
            $report[] = $row;
        }
        
        return $report;
    }

    // Versión optimizada
    public static function cleanupStaleConnections() {
        $instance = self::getInstance();
        $conn = $instance->getConnection();
        
        // Usar un índice adecuado y limitar el número de actualizaciones por lote
        $query = "UPDATE tb_conexiones_log 
                SET estado = 'TIMEOUT',
                    fecha_desconexion = NOW(),
                    duracion = TIMESTAMPDIFF(SECOND, fecha_conexion, NOW()),
                    tcp_state = 0
                WHERE estado = 'ACTIVE' 
                AND fecha_conexion < DATE_SUB(NOW(), INTERVAL 5 MINUTE)
                LIMIT 1000";  // Procesar en lotes para evitar bloqueo de tabla
                
        $conn->query($query);
        
        return $conn->affected_rows;
    }

    public function closeInactiveConnections() {
        try {
            // Limpiar conexiones inactivas
            $this->cleanupStaleConnections();
            
            // Actualizar estadísticas
            if ($this->connection && $this->connection->ping()) {
                $query = "SELECT COUNT(*) as total FROM tb_conexiones_log WHERE estado = 'ACTIVE'";
                $result = $this->connection->query($query);
                if ($result) {
                    $row = $result->fetch_assoc();
                    $this->activeConnections = $row['total'];
                    $result->free();
                }
            }
        } catch (Exception $e) {
            error_log("Error en closeInactiveConnections: " . $e->getMessage());
        }
    }
}

/**
 * Custom Security Exception Classes
 * These provide specific error types while maintaining security
 */

/**
 * Database Connection Exception
 * Used for connection-related errors with user-safe messages
 */
class DatabaseConnectionException extends Exception {
    public function __construct($message = "", $code = 0, Throwable $previous = null) {
        // Ensure message is always user-safe
        $safeMessage = $message ?: getUserSafeErrorMessage('database');
        parent::__construct($safeMessage, $code, $previous);
    }
}

/**
 * Database Security Exception
 * Used for security-related database issues
 */
class DatabaseSecurityException extends Exception {
    public function __construct($message = "", $code = 0, Throwable $previous = null) {
        // Log security exception but don't expose details
        logSecureError('Database security exception triggered', [
            'original_message' => $message,
            'code' => $code
        ], 'SECURITY');

        // Always use generic message for security exceptions
        parent::__construct(getUserSafeErrorMessage('general'), $code, $previous);
    }
}

/**
 * Database Query Exception
 * Used for query-related errors with sanitized messages
 */
class DatabaseQueryException extends Exception {
    public function __construct($message = "", $code = 0, Throwable $previous = null) {
        // Sanitize message to remove sensitive information
        $safeMessage = $this->sanitizeQueryError($message);
        parent::__construct($safeMessage, $code, $previous);
    }

    private function sanitizeQueryError($message) {
        // Remove table names, column names, and other sensitive info
        $patterns = [
            '/Table \'[^\']+\'/i' => 'Table',
            '/Column \'[^\']+\'/i' => 'Column',
            '/Database \'[^\']+\'/i' => 'Database',
            '/for key \'[^\']+\'/i' => 'for key',
            '/Duplicate entry \'[^\']+\'/i' => 'Duplicate entry'
        ];

        $sanitized = $message;
        foreach ($patterns as $pattern => $replacement) {
            $sanitized = preg_replace($pattern, $replacement, $sanitized);
        }

        // If message still looks technical, use generic message
        if (preg_match('/SQL|MySQL|MariaDB|SELECT|INSERT|UPDATE|DELETE/i', $sanitized)) {
            return getUserSafeErrorMessage('general');
        }

        return $sanitized;
    }
}