/**
 * Compatibility Layer para los Sistemas de Lazy Loading
 * 
 * Este script detecta y soluciona conflictos entre:
 * - lazy-loading.js (sistema original)
 * - lazy-loading-enhanced.js (sistema v2)
 * - modular-lazy-loading.js (sistema v3)
 * 
 * Autor: Claude
 * Fecha: 28/06/2025
 */

(function() {
    'use strict';

    // Registrar tiempo de inicio para debug
    const startTime = performance.now();
    
    // Variable global para activar el modo debug
    let debugMode = window.location.hash === '#debug';
    
    // Función para logs condicionales
    function log(message, level = 'info') {
        if (!debugMode) return;
        
        const timestamp = new Date().toTimeString().slice(0, 8);
        
        switch(level) {
            case 'error':
                console.error(`[LazyLoading:Compat] [${timestamp}] 🛑 ${message}`);
                break;
            case 'warn':
                console.warn(`[LazyLoading:Compat] [${timestamp}] ⚠️ ${message}`);
                break;
            case 'success':
                console.log(`[LazyLoading:Compat] [${timestamp}] ✅ ${message}`);
                break;
            default:
                console.log(`[LazyLoading:Compat] [${timestamp}] ℹ️ ${message}`);
        }
    }
    
    // Detectar si estamos en una página específica que requiere atención especial
    const isSpecialPage = [
        'home_bodega_new.php'
    ].some(page => window.location.href.includes(page));
    
    if (isSpecialPage) {
        log(`Detectada página que requiere compatibilidad especial: ${window.location.href}`, 'warn');
    }

    /**
     * Crea una función proxy para detectar colisiones entre los sistemas
     */
    function createCompatibilityLayer() {
        log('Inicializando capa de compatibilidad para lazy loading');
        
        // Array para rastrear los sistemas de lazy loading detectados
        const detectedSystems = [];
        
        // Variables para almacenar las implementaciones originales
        let originalDispatchEvent = window.dispatchSectionLoadedEvent;
        let originalLazyLoader = window.LazyLoader;
        let originalLazyLoadingSystem = window.LazyLoadingSystem;
        let originalModularLoader = window.ModularLoader;
        
        // Verificar sistemas existentes
        if (originalDispatchEvent) detectedSystems.push('lazy-loading.js');
        if (originalLazyLoadingSystem) detectedSystems.push('lazy-loading-enhanced.js');
        if (originalModularLoader) detectedSystems.push('modular-lazy-loading.js');
        
        log(`Sistemas de lazy loading detectados: ${detectedSystems.join(', ') || 'ninguno'}`);
        
        // Si hay más de un sistema, mostrar advertencia
        if (detectedSystems.length > 1) {
            log(`Detectados múltiples sistemas de lazy loading, esto puede causar conflictos`, 'warn');
            
            // Si estamos en una página específica con problemas conocidos, aplicar solución
            if (isSpecialPage) {
                log('Aplicando solución específica para página con problemas conocidos', 'warn');
                
                // Para home_bodega_new.php
                if (window.location.href.includes('home_bodega_new.php')) {
                    applyHomeBodegaFix();
                }
            }
        }
        
        // Solución para home_bodega_new.php
        function applyHomeBodegaFix() {
            log('Aplicando solución para home_bodega_new.php');
            
            // Monitorear errores en consulta de semáforo
            monitorSemaforo();
            
            // Verificar errores de carga de recursos
            monitorResourceLoading();
        }
        
        // Monitorear errores en consulta del semáforo
        function monitorSemaforo() {
            // Buscar elementos de error del semáforo periódicamente
            const checkInterval = setInterval(() => {
                const errorAlert = document.querySelector('.alert-warning');
                if (errorAlert && errorAlert.textContent.includes('Error al cargar los datos del semáforo')) {
                    log('Detectado error en consulta del semáforo', 'error');
                    clearInterval(checkInterval);
                    
                    // Intentar recuperar la consulta
                    recoverSemaforoData();
                }
            }, 1000);
            
            // Detener el intervalo después de 10 segundos
            setTimeout(() => {
                clearInterval(checkInterval);
            }, 10000);
        }
        
        // Intentar recuperar datos del semáforo cuando falla
        function recoverSemaforoData() {
            log('Intentando recuperar datos del semáforo');
            
            // Si no hemos intentado recargar aún
            if (!sessionStorage.getItem('semaforo_recovery_attempted')) {
                sessionStorage.setItem('semaforo_recovery_attempted', 'true');
                
                // Mostrar mensaje en consola
                log('Reintentando carga de semáforo en 3 segundos...', 'warn');
                
                setTimeout(() => {
                    location.reload();
                }, 3000);
            }
        }
        
        // Monitorear errores de carga de recursos
        function monitorResourceLoading() {
            // Verificar scripts críticos inmediatamente
            setTimeout(checkCriticalScripts, 500);
            
            // Monitorear errores futuros de recursos
            window.addEventListener('error', function(e) {
                if (e.target && (e.target.tagName === 'SCRIPT' || e.target.tagName === 'LINK')) {
                    const url = e.target.src || e.target.href;
                    if (url) {
                        // Errores de bootstrap - baja prioridad
                        if (url.includes('bootstrap.min.css')) {
                            log(`Error no crítico al cargar bootstrap: ${url}`, 'warn');
                            return; // No bloquear por errores de bootstrap
                        }
                        
                        // Detectar errores en scripts de lazy loading
                        if (url.includes('lazy-loading')) {
                            log(`Error al cargar recurso: ${url}`, 'error');
                            
                            if (!sessionStorage.getItem('resource_error_logged')) {
                                sessionStorage.setItem('resource_error_logged', 'true');
                                
                                // Intentar solucionar automáticamente
                                injectLazyLoadingFallback(url);
                                
                                // Registrar en consola explicación detallada
                                console.info(`
======= PROBLEMA DETECTADO Y SOLUCIONADO =======
Se detectó un error al cargar: ${url}
Pero se ha aplicado una solución automática que debería resolver el problema.
=============================================
                                `);
                            }
                        }
                    }
                }
            }, true);
        }
        
        // Verificar proactivamente scripts críticos
        function checkCriticalScripts() {
            const criticalScripts = [
                { name: 'lazy-loading.js', variable: 'LazyLoader' },
                { name: 'lazy-loading-enhanced.js', variable: 'LazyLoadingSystem' },
                { name: 'modular-lazy-loading.js', variable: 'ModularLoader' }
            ];
            
            // Verificar cada script crítico
            let missingScripts = criticalScripts.filter(script => {
                return !window[script.variable];
            });
            
            if (missingScripts.length > 0) {
                log(`Scripts críticos faltantes: ${missingScripts.map(s => s.name).join(', ')}`, 'warn');
                
                // Intentar inyectar fallbacks para scripts faltantes
                missingScripts.forEach(script => {
                    injectLazyLoadingFallback(`js/${script.name}`);
                });
            }
        }
        
        // Inyectar fallback para lazy-loading
        function injectLazyLoadingFallback(failedUrl) {
            // Solo si no hemos intentado ya una solución
            if (sessionStorage.getItem('fallback_injected')) return;
            
            log(`Aplicando solución para script faltante: ${failedUrl}`, 'warn');
            sessionStorage.setItem('fallback_injected', 'true');
            
            // Si fallaba lazy-loading.js, asegurar que el API básico esté disponible
            if (!window.LazyLoader) {
                log('Creando API de compatibilidad para LazyLoader', 'info');
                
                // Crear un objeto LazyLoader básico si no existe
                window.LazyLoader = {
                    loadSection: function(sectionId) {
                        log(`[Fallback] Cargando sección: ${sectionId}`, 'info');
                        // Intentar cargar con otro sistema disponible
                        if (window.LazyLoadingSystem && typeof window.LazyLoadingSystem.loadSection === 'function') {
                            return window.LazyLoadingSystem.loadSection(sectionId);
                        }
                        if (window.ModularLoader && typeof window.ModularLoader.loadSection === 'function') {
                            return window.ModularLoader.loadSection(sectionId);
                        }
                        return Promise.resolve('No action taken (fallback)');
                    },
                    reloadSection: function(sectionId) {
                        log(`[Fallback] Recargando sección: ${sectionId}`, 'info');
                        location.reload(); // Solución simple: recargar página
                        return true;
                    },
                    preloadSections: function() {
                        // No hacer nada en el fallback
                        return true;
                    }
                };
            }
            
            // Asegurar que la función dispatchSectionLoadedEvent exista
            if (!window.dispatchSectionLoadedEvent) {
                log('Creando función dispatchSectionLoadedEvent', 'info');
                
                window.dispatchSectionLoadedEvent = function(sectionId) {
                    log(`[Fallback] Disparando evento para sección: ${sectionId}`, 'info');
                    
                    // Crear y disparar evento
                    const event = new CustomEvent('sectionLoaded', {
                        detail: { sectionId: sectionId },
                        bubbles: true
                    });
                    document.dispatchEvent(event);
                };
            }
            
            log('Solución de compatibilidad aplicada correctamente', 'success');
        }
        }
        
        // Crear funciones proxy para window.dispatchSectionLoadedEvent
        if (originalDispatchEvent) {
            window.dispatchSectionLoadedEvent = function(...args) {
                log(`Llamada a dispatchSectionLoadedEvent - Sección: ${args[0]}`);
                return originalDispatchEvent.apply(this, args);
            };
        }
        
        // Crear compatibilidad entre sistemas
        window.LazyLoadingCompatibility = {
            debug: debugMode,
            detectedSystems: detectedSystems,
            
            // Activar modo debug
            enableDebug: function() {
                debugMode = true;
                log('Modo debug activado', 'success');
                
                // Activar debug en todos los sistemas detectados
                if (window.LazyLoadingSystem && typeof window.LazyLoadingSystem.enableDebug === 'function') {
                    window.LazyLoadingSystem.enableDebug();
                }
                
                return true;
            },
            
            // Detectar y mostrar sistemas activos
            detectSystems: function() {
                return {
                    originalSystem: typeof window.LazyLoader !== 'undefined',
                    enhancedSystem: typeof window.LazyLoadingSystem !== 'undefined',
                    modularSystem: typeof window.ModularLoader !== 'undefined'
                };
            },
            
            // Recargar una sección específica
            reloadSection: function(sectionId) {
                log(`Intentando recargar sección: ${sectionId}`);
                
                // Intentar con cada sistema disponible
                if (window.LazyLoader && typeof window.LazyLoader.reloadSection === 'function') {
                    window.LazyLoader.reloadSection(sectionId);
                    return true;
                }
                
                if (window.LazyLoadingSystem && typeof window.LazyLoadingSystem.reloadSection === 'function') {
                    window.LazyLoadingSystem.reloadSection(sectionId);
                    return true;
                }
                
                if (window.ModularLoader && typeof window.ModularLoader.reloadSection === 'function') {
                    window.ModularLoader.reloadSection(sectionId);
                    return true;
                }
                
                log(`No se encontró ningún sistema disponible para recargar la sección`, 'error');
                return false;
            }
        };
        
        // Registrar tiempo de inicialización
        const initTime = performance.now() - startTime;
        log(`Capa de compatibilidad inicializada en ${initTime.toFixed(2)}ms`, 'success');
    }
    
    // Inicializar capa de compatibilidad cuando el DOM esté listo
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', createCompatibilityLayer);
    } else {
        createCompatibilityLayer();
    }
})();